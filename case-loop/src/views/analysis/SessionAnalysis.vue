<template>
  <div class="session-analysis">
    <!-- 筛选区域 -->
    <a-card class="filter-card" :bordered="false">
      <a-form layout="vertical" class="compact-form">
        <a-row :gutter="16">
          <!-- 会话ID -->
          <a-col :span="8">
            <a-form-item label="会话ID" class="compact-form-item">
              <a-input v-model:value="searchFormData.sessionId" placeholder="请输入会话ID" allow-clear/>
            </a-form-item>
          </a-col>

          <!-- 创建时间 -->
          <a-col :span="8">
            <a-form-item label="创建时间" class="compact-form-item">
              <a-range-picker
                  v-model:value="searchFormData.date"
                  style="width: 100%"
                  :format="dateFormat"
                  :disabledDate="disabledDate"
                  :placeholder="['开始时间', '结束时间']"
              />
            </a-form-item>
          </a-col>

          <!-- 空间/应用 -->
          <a-col :span="8">
            <a-form-item label="空间/应用" class="compact-form-item">
              <workspace-app-select
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  @change="handleWorkspaceAppChange"
              />
            </a-form-item>
          </a-col>

          <!-- 应用版本 -->
          <a-col :span="8">
            <a-form-item label="应用版本" class="compact-form-item">
              <app-version-select
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  :is-applying-saved-filter="isApplyingSavedFilter"
                  @change="handleAppVersionChange"
              />
            </a-form-item>
          </a-col>

          <!-- 场景 -->
          <a-col :span="8" v-if="isFilterAdded('scene')">
            <a-form-item label="场景" class="compact-form-item">
              <scene-select
                  v-model:value="searchFormData.scene"
                  :form-data="searchFormData"
                  :scene-relation-config="sceneRelationConfig"
                  @change="handleSceneChange"
              />
            </a-form-item>
          </a-col>

          <!-- VisitID -->
          <a-col :span="8" v-if="isFilterAdded('visitId')">
            <a-form-item label="VisitID" class="compact-form-item">
              <a-input v-model:value="searchFormData.visitId" placeholder="请输入VisitID" allow-clear/>
            </a-form-item>
          </a-col>

          <!-- 更多筛选项 (根据配置动态添加) -->
          <template v-for="filter in visibleFilters" :key="filter.key">
            <a-col :span="8"
                   v-if="isFilterAdded(filter.key) && !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId'].includes(filter.key)">
              <a-form-item :label="filter.label" class="compact-form-item">
                <!-- 选择框类型 -->
                <a-select
                    v-if="filter.type === 'select'"
                    v-model:value="searchFormData[filter.key]"
                    :placeholder="`请选择${filter.label}`"
                    allow-clear
                    :mode="filter.multiple ? 'multiple' : undefined"
                >
                  <a-select-option
                      v-for="option in getFilterOptions(filter.key)"
                      :key="option.value"
                      :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>

                <!-- 输入框类型 -->
                <a-input
                    v-else-if="filter.type === 'input'"
                    v-model:value="searchFormData[filter.key]"
                    :placeholder="`请输入${filter.label}`"
                    allow-clear
                />

                <!-- 特殊组件类型 -->
                <component
                    v-else-if="filter.type === 'component'"
                    :is="filter.component"
                    v-model:value="searchFormData[filter.key]"
                    :form-data="searchFormData"
                    :scene-relation-config="sceneRelationConfig"
                    :is-applying-saved-filter="isApplyingSavedFilter"
                    @change="componentChangeHandlers[filter.key] || (() => {})"
                />
              </a-form-item>
            </a-col>
          </template>
        </a-row>

        <!-- 操作按钮 -->
        <a-row>
          <a-col :span="24" style="text-align: right">
            <a-space>
              <a-button @click="handleAddFilterClick" class="custom-button">
                <template #icon>
                  <PlusOutlined/>
                </template>
                添加筛选条件
              </a-button>
              <a-button @click="handleClearClick" class="custom-button">
                <template #icon>
                  <ReloadOutlined/>
                </template>
                重置
              </a-button>
              <a-button type="primary" @click="handleSearch" class="custom-button">
                <template #icon>
                  <SearchOutlined/>
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 筛选条件添加模态框 -->
    <filter-condition-modal
        v-model:visible="filterConditionModalVisible"
        :available-filters="availableFilters"
        :selected-filters="selectedFilters"
        @confirm="handleFilterConfirm"
    />

    <!-- 数据表格 -->
    <a-card style="margin-top: 16px" :bordered="false">
      <template #title>
        <div class="table-header">
          <div class="table-title">会话列表</div>
          <div class="table-actions">
            <a-space>
              <favorite-filters-dropdown
                  :favorites="favoriteFilters"
                  @select="applyFavoriteFilter"
                  @remove="handleDeleteFavoriteFilter"
                  @manage="openManageFavoritesModal"
              />
              <save-filter-button
                  :current-filter="currentFilter"
                  @save="openSaveFilterModal"
              />
              <a-button
                  :disabled="!hasSelectedRows"
                  @click="handleExportSelectedClick"
                  class="custom-button"
              >
                <template #icon>
                  <ExportOutlined/>
                </template>
                导出选中
              </a-button>
            </a-space>
          </div>
        </div>
      </template>

      <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :row-key="record => record.sessionId"
          @change="handleTableChange"
      >
        <!-- 会话ID列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'sessionId'">
            <a @click="viewSessionDetail(record)">{{ record.sessionId }}</a>
          </template>

          <!-- 空间/应用列 -->
          <template v-if="column.key === 'workspaceApp'">
            <div v-if="record.workspaceAppInfo && record.workspaceAppInfo.length > 0">
              <a-tag color="blue" v-for="ws in record.workspaceAppInfo" :key="ws.workspaceId">
                {{ ws.workspaceName }}
              </a-tag>
              <a-tag color="green" v-for="app in record.workspaceAppInfo.flatMap(ws => ws.applicationList)"
                     :key="app.applicationId">
                {{ app.applicationName }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 场景列 -->
          <template v-if="column.dataIndex === 'sceneName'">
            <a-tag v-if="record.sceneName" :color="getScenarioColor(record.sceneName)">
              {{ record.sceneName }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 转人工列 -->
          <template v-if="column.dataIndex === 'transferStaff'">
            <template v-if="record.transferStaff !== null && record.transferStaff !== undefined">
              <a-tag :color="getTransferStaffTagColor(record.transferStaff)">
                {{ getTransferStaffTagText(record.transferStaff) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 解决情况列 -->
          <template v-if="column.dataIndex === 'sessionSolved'">
            <template v-if="record.sessionSolved !== null && record.sessionSolved !== undefined">
              <a-tag :color="getSolvedTagColor(record.sessionSolved)">
                {{ getSolvedTagText(record.sessionSolved) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 满意度列 -->
          <template v-if="column.dataIndex === 'stars'">
            <div v-if="record.stars !== null && record.stars !== undefined">
              <a-rate :value="record.stars" disabled :count="5"/>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a @click="viewSessionDetail(record)">查看</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 保存筛选条件模态框 -->
    <save-filter-modal
        v-model:visible="saveFilterModalVisible"
        :filter-data="currentFilter"
        @save="handleSaveFilter"
    />
  </div>
</template>

<script>
import {defineComponent, ref, reactive, computed, onMounted, watch} from 'vue';
import {message} from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined
} from '@ant-design/icons-vue';
import {useRouter} from 'vue-router';
import dayjs from 'dayjs';

// 导入自定义组件
import WorkspaceAppSelect from './components/WorkspaceAppSelect.vue';
import AppVersionSelect from './components/AppVersionSelect.vue';
import SceneSelect from './components/SceneSelect.vue';
import FilterConditionModal from './components/FilterConditionModal.vue';
import FavoriteFiltersDropdown from './components/FavoriteFiltersDropdown.vue';
import SaveFilterButton from './components/SaveFilterButton.vue';
import SaveFilterModal from './components/SaveFilterModal.vue';

// 导入API
import {
  getSessionConditionConfig,
  getSessionConditionScene,
  getSessionPage,
  // Define placeholders for API functions if they are not already in @/api/analysis
  // These should correspond to actual implementations or further stubs in your API file.
  deleteSavedFilter, // Assuming this will be: export const getFavoriteFiltersAPI = (analysisType) => { ... }
  getSavedFilterList,   // Assuming this will be: export const saveFavoriteFilterAPI = (payload) => { ... }
  saveFilterCondition  // Assuming this will be: export const deleteFavoriteFilterAPI = (filterId) => { ... }
} from '@/api/analysis';

const LOCAL_STORAGE_KEY = 'sessionAnalysisFilters';

// Placeholder for the analysis type string needed by favorite filter APIs
const FAVORITE_FILTER_ANALYSIS_TYPE = 'online'; // Adjust if a different value is known

export default defineComponent({
  name: 'SessionAnalysis',
  components: {
    WorkspaceAppSelect,
    AppVersionSelect,
    SceneSelect,
    FilterConditionModal,
    FavoriteFiltersDropdown,
    SaveFilterButton,
    SaveFilterModal,
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    ExportOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const tableData = ref([]);

    // 筛选表单数据
    const searchFormData = reactive({
      sessionId: '',
      date: [
        dayjs().subtract(7, 'day').startOf('day'),
        dayjs().endOf('day')
      ],
      workspaceAndApp: null,
      applicationVersionId: null,
      scene: null,
      visitId: ''
    });

    // 场景关系配置
    const sceneRelationConfig = ref({});

    // 筛选条件模态框
    const filterConditionModalVisible = ref(false);
    const availableFilters = ref([]);
    const selectedFilters = ref([]);
    const isApplyingSavedFilter = ref(false);

    // 表格列配置
    const columns = [
      {
        title: '会话ID',
        dataIndex: 'sessionId',
        key: 'sessionId',
        width: 200,
        fixed: 'left'
      },
      {
        title: '创建时间',
        dataIndex: 'time',
        key: 'time',
        width: 170,
        sorter: true,
        customRender: ({text}) => {
          return text ? new Date(text).toLocaleString() : '-';
        }
      },
      {
        title: '空间/应用',
        key: 'workspaceApp',
        width: 150,
        customRender: ({record}) => {
          if (record.workspaceAppInfo && record.workspaceAppInfo.length > 0) {
            const workspaceNames = record.workspaceAppInfo.map(ws => ws.workspaceName);
            const appNames = record.workspaceAppInfo.flatMap(ws =>
                ws.applicationList.map(app => app.applicationName)
            );
            return `${workspaceNames.join(', ')}/${appNames.join(', ')}`;
          }
          return '-';
        }
      },
      {
        title: '场景',
        dataIndex: 'sceneName',
        key: 'sceneName',
        width: 150
      },
      
      {
        title: '转人工',
        dataIndex: 'transferStaff',
        key: 'transferStaff',
        width: 100
      },
      {
        title: '是否解决',
        dataIndex: 'sessionSolved',
        key: 'sessionSolved',
        width: 100
      },
      {
        title: '满意度',
        dataIndex: 'stars',
        key: 'stars',
        width: 150
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100,
        fixed: 'right'
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 表格选择
    const selectedRowKeys = ref([]);
    const hasSelectedRows = computed(() => selectedRowKeys.value.length > 0);

    // 收藏的筛选条件
    const favoriteFilters = ref([]);
    const currentFilter = reactive({
      name: '',
      conditions: {}
    });

    // 保存筛选条件模态框
    const saveFilterModalVisible = ref(false);

    // 可见的筛选项
    const visibleFilters = computed(() => {
      return selectedFilters.value.filter(filter => !filter.hidden);
    });

    // 处理工作空间应用变更
    const handleWorkspaceAppChange = (value) => {
      searchFormData.workspaceAndApp = value;
      // 清空依赖项
      searchFormData.applicationVersionId = null;
      searchFormData.scene = null;
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理应用版本变更
    const handleAppVersionChange = (value) => {
      searchFormData.applicationVersionId = value;
      // 清空依赖项
      searchFormData.scene = null;
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理场景变更
    const handleSceneChange = (value) => {
      searchFormData.scene = value;
      // 清空依赖项
      searchFormData.bu = null;
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理业务变更
    const handleBusinessChange = (value) => {
      searchFormData.bu = value;
      // 清空依赖项
      searchFormData.subBu = null;
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理子业务变更
    const handleSubBusinessChange = (value) => {
      searchFormData.subBu = value;
      // 清空依赖项
      searchFormData.taskKey = null;
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理任务键变更
    const handleTaskKeyChange = (value) => {
      searchFormData.taskKey = value;
      // 清空依赖项
      searchFormData.taskVersion = null;
      searchFormData.taskNode = null;
    };

    // 处理任务版本变更
    const handleTaskVersionChange = (value) => {
      searchFormData.taskVersion = value;
      // 清空依赖项
      searchFormData.taskNode = null;
    };

    // 处理任务节点变更
    const handleTaskNodeChange = (value) => {
      searchFormData.taskNode = value;
    };

    // 组件变更处理器
    const componentChangeHandlers = {
      taskKey: handleTaskKeyChange,
      taskVersion: handleTaskVersionChange,
      taskNode: handleTaskNodeChange,
      workspaceAndApp: handleWorkspaceAppChange,
      applicationVersionId: handleAppVersionChange,
      scene: handleSceneChange,
      bu: handleBusinessChange,
      subBu: handleSubBusinessChange
    };

    // 初始化
    onMounted(async () => {
      await fetchFilterConfig();
      await fetchSceneRelationConfig();
      await loadFavoriteFilters();
      fetchData();
    });

    // 获取筛选配置
    const fetchFilterConfig = async () => {
      try {
        const res = await getSessionConditionConfig();
        console.log('筛选配置返回数据:', res); // 添加日志输出，方便调试

        // 修改判断条件，使用code === 0判断请求是否成功
        if (res.code === 0) {
          // 适配原始应用的返回格式
          // 原始应用返回格式: { code: 0, data: { filterModules: [...] } }
          const filterModules = res.data?.filterModules || [];
          const allFilters = [];

          // 处理所有模块的筛选项
          filterModules.forEach(module => {
            if (module.filters && Array.isArray(module.filters)) {
              // 转换筛选项格式
              const moduleFilters = module.filters.map(filter => ({
                key: filter.id, // 使用id作为key
                label: filter.name, // 使用name作为label
                type: getFilterType(filter.type), // 转换类型
                options: filter.options || [],
                multiple: filter.type === 'multiSelect', // 是否是多选
                required: false, // 默认非必选
                hidden: false, // 默认不隐藏
                selected: ['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId'].includes(filter.id) // 默认选中基础筛选项
              }));

              allFilters.push(...moduleFilters);
            }
          });

          availableFilters.value = allFilters;

          // 默认选中基础筛选项
          selectedFilters.value = availableFilters.value.filter(filter =>
              filter.required || filter.selected
          );
        } else {
          message.error(res.message || '获取筛选配置失败');
        }
      } catch (error) {
        console.error('获取筛选配置失败', error);
        message.error('获取筛选配置失败');
      }
    };

    // 根据API返回的filter.type转换为组件适用的type
    const getFilterType = (apiType) => {
      // 根据原始应用的类型映射
      switch (apiType) {
        case 'input':
          return 'input';
        case 'select':
          return 'select';
        case 'date':
          return 'date';
        case 'multiSelect':
          return 'select'; // 多选select
        default:
          return 'select';
      }
    };

    // 处理场景关系配置
    const fetchSceneRelationConfig = async () => {
      try {
        const res = await getSessionConditionScene();
        console.log('场景关系配置返回数据:', res); // 添加日志输出，方便调试

        // 修改判断条件，使用code === 0判断请求是否成功
        if (res.code === 0) {
          // 适配原始应用的返回格式
          // 原始应用返回格式: { code: 0, data: { sceneInfos: [...] } }
          sceneRelationConfig.value = res.data || {};
        } else {
          message.error(res.message || '获取场景关系配置失败');
        }
      } catch (error) {
        console.error('获取场景关系配置失败', error);
        message.error('获取场景关系配置失败');
      }
    };

    // 获取表格数据
    const fetchData = async (page = pagination.current, pageSize = pagination.pageSize) => {
      loading.value = true;
      try {
        // 构建查询参数
        const params = {
          ...buildQueryParams()
        };

        // pageNum和pageSize已经在buildQueryParams中设置了
        // 如果直接传入了page和pageSize，则覆盖buildQueryParams中的值
        if (page !== pagination.current) {
          params.pageNum = page;
        }

        if (pageSize !== pagination.pageSize) {
          params.pageSize = pageSize;
        }

        console.log('请求参数:', params); // 添加日志输出，方便调试
        const res = await getSessionPage(params);
        console.log('API返回数据:', res); // 添加日志输出，方便调试

        // 检查API返回状态
        if (res.code === 0) { // 原始应用API成功状态码为0
          // 适配原始应用的返回格式
          // 原始应用返回格式: { code: 0, data: { data: [...], totalNum: number, cost: number } }
          const responseData = res.data?.data || [];

          // 处理数据，确保字段正确映射
          tableData.value = responseData.map(item => ({
            ...item,
            // 确保这些字段存在，如果不存在则使用默认值
            sessionSolved: item.sessionSolved || null,
            stars: item.stars !== undefined ? item.stars : null,
            transferStaff: item.transferStaff || null
          }));

          console.log('处理后的表格数据:', tableData.value); // 添加日志输出，方便调试
          pagination.total = res.data?.totalNum || 0;
        } else {
          message.error(res.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取数据失败', error);
        message.error('获取数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 构建查询参数
    const buildQueryParams = () => {
      // 创建基础参数
      const params = {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        channel: 'ONLINE', // 在线会话类型
        sessionId: searchFormData.sessionId || '',
        visitId: searchFormData.visitId || '',
        applicationVersionId: searchFormData.applicationVersionId || '',
        scene: searchFormData.scene || '',
        bu: searchFormData.bu || '',
        subBu: searchFormData.subBu || '',
        taskKey: searchFormData.taskKey || '',
        taskVersion: searchFormData.taskVersion || '',
        taskNode: searchFormData.taskNode || ''
      };

      // 处理日期参数 - 始终包含日期参数
      const date = searchFormData.date;
      if (date && date[0]) {
        params.startTime = date[0].valueOf(); // 转换为时间戳
      } else {
        // 如果没有选择日期，使用默认时间范围（最近7天）
        params.startTime = dayjs().subtract(7, 'day').startOf('day').valueOf();
      }

      if (date && date[1]) {
        params.endTime = date[1].valueOf(); // 转换为时间戳
      } else {
        // 如果没有选择日期，使用当前时间作为结束时间
        params.endTime = dayjs().endOf('day').valueOf();
      }

      // 处理工作空间和应用参数
      const workspaceAndApp = searchFormData.workspaceAndApp;
      if (workspaceAndApp && workspaceAndApp[0]) params.workspaceId = workspaceAndApp[0];
      if (workspaceAndApp && workspaceAndApp[1]) params.applicationId = workspaceAndApp[1];

      // 处理其他动态筛选参数
      Object.keys(searchFormData).forEach(key => {
        // 排除已经处理过的字段和空值
        if (!['date', 'workspaceAndApp', 'sessionId', 'visitId', 'applicationVersionId', 'scene', 'bu', 'subBu', 'taskKey', 'taskVersion', 'taskNode'].includes(key)
            && searchFormData[key] !== null
            && searchFormData[key] !== undefined
            && searchFormData[key] !== '') {
          params[key] = searchFormData[key];
        }
      });

      // 处理排序
      if (searchFormData.sortField && searchFormData.sortOrder) {
        params.sortField = searchFormData.sortField;
        params.sortOrder = searchFormData.sortOrder;
      }

      return params;
    };

    // 处理搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchData(1);
    };

    // 处理清空
    const handleClearClick = () => {
      // 重置表单数据
      const defaultKeys = ['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId'];
      Object.keys(searchFormData).forEach(key => {
        if (key === 'date') {
          searchFormData.date = null;
        } else if (defaultKeys.includes(key)) {
          searchFormData[key] = key === 'sessionId' || key === 'visitId' ? '' : undefined;
        } else {
          // 移除动态添加的key
          delete searchFormData[key];
        }
      });
      // 清除动态添加的筛选条件状态
      selectedFilters.value = [];
      // 清除localStorage
      localStorage.removeItem(LOCAL_STORAGE_KEY);

      pagination.current = 1;
      // 重新加载数据
      fetchData(1);
      // 清空表格数据并重新获取（如果需要立即看到空状态或默认列表）
      // tableData.value = [];
      handleSearch(); // 或者 fetchData();
    };

    // 处理筛选条件添加
    const handleAddFilterClick = () => {
      // 在打开模态框前，更新可用筛选条件的选中状态
      availableFilters.value = availableFilters.value.map(filter => ({
        ...filter,
        selected: selectedFilters.value.some(selected => selected.key === filter.key)
      }));

      filterConditionModalVisible.value = true;
    };

    // 处理筛选条件确认
    const handleFilterConfirm = (filters) => {
      // 更新选中的筛选条件
      selectedFilters.value = filters;
      filterConditionModalVisible.value = false;
    };

    // 判断筛选项是否添加
    const isFilterAdded = (key) => {
      return selectedFilters.value.some(filter => filter.key === key);
    };

    // 获取筛选项选项
    const getFilterOptions = (key) => {
      const filter = availableFilters.value.find(item => item.key === key);
      return filter?.options || [];
    };

    // 监听选中的筛选条件变化，动态显示或隐藏筛选项
    watch(() => selectedFilters.value, (newFilters) => {
      // 当选中的筛选条件变化时，检查是否有新添加的筛选条件需要初始化表单数据
      newFilters.forEach(filter => {
        if (searchFormData[filter.key] === undefined) {
          // 根据筛选条件类型设置默认值
          if (filter.type === 'select' && filter.multiple) {
            searchFormData[filter.key] = []; // 多选默认为空数组
          } else if (filter.type === 'select') {
            searchFormData[filter.key] = null; // 单选默认为null
          } else if (filter.type === 'date') {
            searchFormData[filter.key] = null; // 日期默认为null
          } else {
            searchFormData[filter.key] = ''; // 输入框默认为空字符串
          }
        }
      });
    }, {deep: true});

    // 处理表格变更
    const handleTableChange = (pag, filters, sorter) => {
      // 处理排序
      if (sorter) {
        const {field, order} = sorter;
        searchFormData.sortField = field;
        searchFormData.sortOrder = order === 'ascend' ? 'asc' : order === 'descend' ? 'desc' : undefined;
      } else {
        searchFormData.sortField = undefined;
        searchFormData.sortOrder = undefined;
      }

      // 处理分页
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;

      // 获取数据
      fetchData(pag.current, pag.pageSize);
    };

    // 处理选择变更
    const onSelectChange = (selectedRowKeys) => {
      selectedRowKeys.value = selectedRowKeys;
    };

    // 获取解决标签颜色
    const getSolvedTagColor = (solved) => {
      if (solved === null || solved === undefined) return '';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? 'success' : 'error';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'success',
          'N': 'error',
          'U': '',
          'true': 'success',
          'false': 'error',
          '是': 'success',
          '否': 'error',
          '已解决': 'success',
          '未解决': 'error',
          '未评价': ''
        };

        return mapping[solved] || '';
      }

      // 默认情况
      return '';
    };

    // 获取解决标签文本
    const getSolvedTagText = (solved) => {
      if (solved === null || solved === undefined) return '-';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? '已解决' : '未解决';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        if (solved === 'Y' || solved === 'true' || solved === '是') {
          return '已解决';
        }

        if (solved === 'N' || solved === 'false' || solved === '否') {
          return '未解决';
        }

        if (solved === 'U') {
          return '未评价';
        }

        // 如果已经是完整的文本，直接返回
        if (solved === '已解决' || solved === '未解决' || solved === '未评价') {
          return solved;
        }

        // 如果是其他字符串，直接返回
        return solved;
      }

      // 默认情况
      return '-';
    };

    // 获取场景颜色
    const getScenarioColor = (sceneName) => {
      // 根据场景名称返回不同的颜色
      // 这里使用一些常见场景的映射，可以根据实际需求调整
      const colorMap = {
        '外客在线智能': 'blue',
        '骑手在线智能': 'green',
        '拼好饭在线智能': 'orange',
        '拼好饭在线虚拟客服': 'purple',
        '外商在线智能': 'cyan',
        '外客直出': 'magenta',
        '外客履约': 'blue',
        '外客售后': 'orange',
        '外客全场景': 'green'
      };

      // 如果没有匹配的场景，返回默认颜色
      return colorMap[sceneName] || 'blue';
    };

    // 获取转人工标签颜色
    const getTransferStaffTagColor = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? 'blue' : 'green';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'blue',
          'N': 'green',
          'true': 'blue',
          'false': 'green',
          '是': 'blue',
          '否': 'green',
          '已转人工': 'blue',
          '未转人工': 'green'
        };

        return mapping[transferStaff] || 'blue';
      }

      // 默认情况
      return 'blue';
    };

    // 获取转人工标签文本
    const getTransferStaffTagText = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '-';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? '已转人工' : '未转人工';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        if (transferStaff === 'Y' || transferStaff === 'true' || transferStaff === '是') {
          return '已转人工';
        }

        if (transferStaff === 'N' || transferStaff === 'false' || transferStaff === '否') {
          return '未转人工';
        }

        // 如果已经是完整的文本，直接返回
        if (transferStaff === '已转人工' || transferStaff === '未转人工') {
          return transferStaff;
        }

        // 如果是其他字符串，直接返回
        return transferStaff;
      }

      // 默认情况
      return '已转人工';
    };

    // 查看会话详情
    const viewSessionDetail = (record) => {
      router.push({
        name: 'SessionDetail',
        params: {sessionId: record.sessionId}
      });

      // router.resolve({
      //   name: 'SessionDetail',
      //   params: {sessionId: record.sessionId}
      // }).href

      // window.open(router.resolve({
      //   name: 'SessionDetail',
      //   params: {sessionId: record.sessionId}
      // }).href, '_blank');

    };

    // 处理导出选中
    const handleExportSelectedClick = () => {
      if (!hasSelectedRows.value) {
        message.warning('请先选择要导出的记录');
        return;
      }

      // 导出选中记录的逻辑
      // TODO: 实现导出功能
      message.success(`成功导出 ${selectedRowKeys.value.length} 条记录`);
    };

    // 应用收藏的筛选条件
    const applyFavoriteFilter = (filterToApply) => { // Removed :any type annotation
      isApplyingSavedFilter.value = true;
      // Ensure filterToApply and its properties exist before trying to use them
      const formDataRecord = filterToApply && filterToApply.conditionRecords && Array.isArray(filterToApply.conditionRecords)
                             ? filterToApply.conditionRecords.find((cr) => cr && cr.conditionId === '_savedFormData')
                             : null;

      if (formDataRecord && formDataRecord.conditionValue) {
        const savedFormData = JSON.parse(JSON.stringify(formDataRecord.conditionValue));

        Object.keys(searchFormData).forEach(key => {
          if (key === 'date') searchFormData.date = null;
          else if (typeof searchFormData[key] === 'string') searchFormData[key] = '';
          else searchFormData[key] = undefined;
        });

        Object.keys(savedFormData).forEach(key => {
          if (key === 'date') {
            const dateVal = savedFormData.date;
            if (Array.isArray(dateVal) && dateVal.length === 2) {
                const startDate = dateVal[0] ? dayjs(dateVal[0]) : null;
                const endDate = dateVal[1] ? dayjs(dateVal[1]) : null;
                let parsedDate = null;
                if (startDate && endDate && startDate.isValid() && endDate.isValid()) parsedDate = [startDate, endDate];
                else if (startDate && startDate.isValid()) parsedDate = [startDate, null];
                else if (endDate && endDate.isValid()) parsedDate = [null, endDate];
                searchFormData.date = parsedDate;
            } else {
                searchFormData.date = null;
            }
          } else {
            searchFormData[key] = savedFormData[key];
          }
        });

        const availableFiltersValue = availableFilters.value || [];
        const dynamicKeys = Object.keys(savedFormData).filter(k =>
            !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId'].includes(k) &&
            availableFiltersValue.some(af => af.key === k)
        );
        selectedFilters.value = dynamicKeys;

        message.success(`已应用常用筛选: ${filterToApply.recordName}`);
        handleSearch();
      } else {
        message.error('无法应用常用筛选：格式不正确或筛选条件为空');
      }
      isApplyingSavedFilter.value = false;
    };

    // 打开管理收藏模态框
    const openManageFavoritesModal = () => {
      // TODO: 实现管理收藏模态框
    };

    // 打开保存筛选条件模态框
    const openSaveFilterModal = () => {
      // 设置当前筛选条件
      currentFilter.conditions = {...searchFormData};
      saveFilterModalVisible.value = true;
    };

    // 处理保存筛选条件 - Now targets backend API
    const handleSaveFilter = async (nameFromModal) => {
      if (!nameFromModal.trim()) {
        message.warning('筛选名称不能为空');
        return;
      }
      if (nameFromModal.trim().length > 20) {
        message.warning('筛选名称不能超过20个字符');
        return;
      }

      const conditionsToSave = JSON.parse(JSON.stringify(searchFormData));
      const payload = {
        recordName: nameFromModal.trim(),
        conditionRecords: [
          {
            conditionId: '_savedFormData',
            conditionName: '完整表单数据',
            conditionValue: conditionsToSave,
            conditionDisplayValue: '完整表单数据',
          },
        ],
        analysisType: FAVORITE_FILTER_ANALYSIS_TYPE,
      };

      try {
        console.log('Attempting to save favorite filter...'); // Debug log
        const response = await saveFilterCondition(payload);
        if (response && response.code === 0) {
          message.success('常用筛选保存成功');
          await loadFavoriteFilters(); // Refresh the list of favorite filters
      saveFilterModalVisible.value = false;
        } else {
          message.error(response?.message || '保存常用筛选失败');
        }
      } catch (error) {
        console.error('保存常用筛选失败 API call error:', error);
        message.error('保存常用筛选失败，请稍后重试');
      }
    };

    // 日期禁用
    const disabledDate = (current) => {
      // 禁用未来日期
      return current && current.valueOf() > Date.now();
    };

    // 日期格式
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    // 从 localStorage 加载筛选条件 (for searchFormData)
    const loadFiltersFromLocalStorage = () => {
      const savedFiltersRaw = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedFiltersRaw) {
        try {
          const savedFilters = JSON.parse(savedFiltersRaw);
          // 特殊处理日期范围
          if (savedFilters.date && Array.isArray(savedFilters.date) && savedFilters.date.length === 2) {
            const startDate = savedFilters.date[0] ? dayjs(savedFilters.date[0]) : null;
            const endDate = savedFilters.date[1] ? dayjs(savedFilters.date[1]) : null;
            if (startDate && endDate && startDate.isValid() && endDate.isValid()) {
              searchFormData.date = [startDate, endDate];
            } else if (startDate && startDate.isValid()) {
              searchFormData.date = [startDate, null];
            } else if (endDate && endDate.isValid()) {
              searchFormData.date = [null, endDate];
            }
             else {
              searchFormData.date = null;
            }
          } else {
            searchFormData.date = null;
          }
          // 合并其他筛选条件
          Object.keys(savedFilters).forEach(key => {
            if (key !== 'date') {
              searchFormData[key] = savedFilters[key];
            }
          });

          const availableFiltersValue = availableFilters.value || [];
          const previouslySelectedDynamicKeys = Object.keys(savedFilters).filter(k =>
            !['sessionId', 'date', 'workspaceAndApp', 'applicationVersionId', 'scene', 'visitId'].includes(k) &&
            availableFiltersValue.some(af => af.key === k)
          );
          selectedFilters.value = previouslySelectedDynamicKeys;

        } catch (error) {
          console.error('Failed to parse filters from localStorage:', error);
          localStorage.removeItem(LOCAL_STORAGE_KEY);
        }
      }
    };

    // 保存筛选条件到 localStorage (for searchFormData)
    const saveFiltersToLocalStorage = (filters) => {
      try {
        const filtersToSave = JSON.parse(JSON.stringify(filters));
        if (filtersToSave.date && Array.isArray(filtersToSave.date)) {
          filtersToSave.date = [
            filtersToSave.date[0] ? dayjs(filtersToSave.date[0]).toISOString() : null,
            filtersToSave.date[1] ? dayjs(filtersToSave.date[1]).toISOString() : null,
          ];
        } else {
           filtersToSave.date = null;
        }
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(filtersToSave));
      } catch (error) {
        console.error('Failed to save searchFormData to localStorage:', error);
      }
    };

    // 从 API 加载常用筛选条件
    const loadFavoriteFilters = async () => {
      try {
        // console.log('Attempting to load favorite filters...'); // Debug log
        const response = await getSavedFilterList(FAVORITE_FILTER_ANALYSIS_TYPE);
        if (response && response.code === 0 && Array.isArray(response.data)) {
          favoriteFilters.value = response.data.map(fav => {
            // Assuming the API returns an array of objects,
            // each with at least 'id' and 'recordName'
            const mappedFav = {
              id: fav.id, // Assuming 'id' is the unique identifier for the filter
              name: fav.recordName, // Assuming 'recordName' is the display name
              // Keep other properties if needed by applyFavoriteFilter, like conditionRecords
              conditionRecords: fav.conditionRecords 
            };

            // Date parsing logic for 'conditionValue.date' if it exists in conditionRecords
            const formDataRecord = fav.conditionRecords?.find(cr => cr.conditionId === '_savedFormData');
            if (formDataRecord && formDataRecord.conditionValue && formDataRecord.conditionValue.date) {
              const dateVal = formDataRecord.conditionValue.date;
              if (Array.isArray(dateVal) && dateVal.length === 2) {
                const startDate = dateVal[0] ? dayjs(dateVal[0]) : null;
                const endDate = dateVal[1] ? dayjs(dateVal[1]) : null;
                let parsedDate = null;
                if (startDate && endDate && startDate.isValid() && endDate.isValid()) parsedDate = [startDate, endDate];
                else if (startDate && startDate.isValid()) parsedDate = [startDate, null];
                else if (endDate && endDate.isValid()) parsedDate = [null, endDate];
                // Ensure the date is stored back into the structure expected by applyFavoriteFilter
                if (mappedFav.conditionRecords) {
                    const recordToUpdate = mappedFav.conditionRecords.find(cr => cr.conditionId === '_savedFormData');
                    if (recordToUpdate && recordToUpdate.conditionValue) {
                        recordToUpdate.conditionValue.date = parsedDate;
                    }
                }
              }
            }
            return mappedFav;
          });
        } else {
          favoriteFilters.value = [];
          if (response && response.code !== 0) message.error(response.message || '获取常用筛选列表失败');
          // else if (!response) console.error('getSavedFilterList did not return a response'); // Corrected API name
        }
      } catch (error) {
        console.error('获取常用筛选列表失败 API call error:', error);
        message.error('获取常用筛选列表失败，请稍后重试');
        favoriteFilters.value = [];
      }
    };

    // 删除常用筛选
    const handleDeleteFavoriteFilter = async (filterToDelete) => { // Changed parameter to filter object
      if (!filterToDelete || !filterToDelete.id) {
        message.error('无法删除筛选：缺少必要信息');
        return;
      }
      try {
        // console.log(`Attempting to delete favorite filter with ID: ${filterToDelete.id}`); // Debug log
        const response = await deleteSavedFilter(filterToDelete.id);
        if (response && response.code === 0) {
          message.success(`已移除常用筛选 "${filterToDelete.name}"`); // Made message more specific
          await loadFavoriteFilters(); // Refresh the list
        } else {
          message.error(response?.message || '删除常用筛选失败');
        }
      } catch (error) {
        console.error('删除常用筛选失败 API call error:', error);
        message.error('删除常用筛选失败，请稍后重试');
      }
    };

    return {
      loading,
      tableData,
      searchFormData,
      columns,
      pagination,
      selectedRowKeys,
      hasSelectedRows,
      filterConditionModalVisible,
      availableFilters,
      selectedFilters,
      visibleFilters,
      sceneRelationConfig,
      favoriteFilters,
      currentFilter,
      saveFilterModalVisible,
      isApplyingSavedFilter,
      componentChangeHandlers,
      dateFormat,

      handleSearch,
      handleClearClick,
      handleAddFilterClick,
      handleFilterConfirm,
      isFilterAdded,
      getFilterOptions,
      handleWorkspaceAppChange,
      handleAppVersionChange,
      handleSceneChange,
      handleBusinessChange,
      handleSubBusinessChange,
      handleTaskKeyChange,
      handleTaskVersionChange,
      handleTaskNodeChange,
      handleTableChange,
      onSelectChange,
      getSolvedTagColor,
      getSolvedTagText,
      getScenarioColor,
      getTransferStaffTagColor,
      getTransferStaffTagText,
      viewSessionDetail,
      handleExportSelectedClick,
      applyFavoriteFilter,
      openManageFavoritesModal,
      openSaveFilterModal,
      handleSaveFilter,
      disabledDate,
      loadFiltersFromLocalStorage,
      saveFiltersToLocalStorage,
      loadFavoriteFilters,
      handleDeleteFavoriteFilter,
    };
  }
});
</script>

<style scoped>
.session-analysis {
  padding: 20px;
}

.filter-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.compact-form {
  width: 100%;
}

.compact-form-item {
  margin-bottom: 12px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
}

.table-actions {
  display: flex;
  align-items: center;
}

:deep(.ant-table-column-title) {
  font-weight: 500;
}

.custom-button {
  border-radius: 8px;
  transition: all 0.3s;
  margin-right: 8px;
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>