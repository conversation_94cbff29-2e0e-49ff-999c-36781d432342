import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import nightwatchPlugin from 'vite-plugin-nightwatch'
import path from 'path'
// https://vite.dev/config/
export default defineConfig(({mode}) => {
    // 根据当前工作目录中的 `mode` 加载 .env 文件
    const env = loadEnv(mode, process.cwd())

    return {
        plugins: [
            vue(),
            vueJsx(),
            vueDevTools(),
            nightwatchPlugin(),
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src')
            },
        },
        css: {
            preprocessorOptions: {
                less: {
                    javascriptEnabled: true,
                    modifyVars: {
                        // 在这里可以添加自定义的 Less 变量
                    }
                }
            }
        },
        define: {
            'import.meta.env.VITE_SSO_HOST': JSON.stringify(env.VITE_SSO_HOST),
            'import.meta.env.VITE_SSO_CLIENT_ID': JSON.stringify(env.VITE_SSO_CLIENT_ID),
            'import.meta.env.DEV': JSON.stringify(mode === 'development'),
        },
        base: '/',
        server: {
            host: true, // 监听所有地址
            port: parseInt(env.VITE_APP_PORT || '3000'),
            strictPort: true, // 端口被占用时直接退出
            open: true, // 自动打开浏览器
            cors: true, // 允许跨域
            proxy: {
                '/sso-exchange-token': 'http://eval.csp.test.sankuai.com',
                '/_next': 'http://eval.csp.test.sankuai.com',
                '/api': 'http://eval.csp.test.sankuai.com',
                '/api-pb': 'http://eval.csp.test.sankuai.com',
            },
        },
        // 生产环境配置
        build: {
            outDir: 'dist',
            assetsDir: 'assets',
            // 生产环境移除 console
            terserOptions: {
                compress: {
                    drop_console: env.VITE_APP_BUILD_DROP_CONSOLE === 'true',
                    drop_debugger: env.VITE_APP_BUILD_DROP_DEBUGGER === 'true'
                }
            },
            // 构建后是否生成 source map 文件
            sourcemap: env.VITE_APP_BUILD_SOURCEMAP === 'true'
        }
    }
})
