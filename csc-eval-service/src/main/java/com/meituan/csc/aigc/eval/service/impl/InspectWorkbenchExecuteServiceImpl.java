package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.*;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.Cat;
import com.dianping.csc.portal.message.dto.MessageDTO;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.StoreBoundResponse;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.csc.aigc.agent.enums.reply.ReplyEndTypeEnum;
import com.meituan.csc.aigc.eval.config.http.HttpConfig;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.constants.RedisConstants;
import com.meituan.csc.aigc.eval.constants.WorkbenchConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dao.service.generator.*;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.aida.AidaAppTreeDTO;
import com.meituan.csc.aigc.eval.dto.aida.AidaRobotDTO;
import com.meituan.csc.aigc.eval.dto.dataset.SingleTemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionEsDTO;
import com.meituan.csc.aigc.eval.dto.es.OnlineSessionSearchDTO;
import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.dto.gpt.GptRequestDTO;
import com.meituan.csc.aigc.eval.dto.ivr.*;
import com.meituan.csc.aigc.eval.dto.workbench.*;
import com.meituan.csc.aigc.eval.dto.workbench.AidaSessionInfoDTO;
import com.meituan.csc.aigc.eval.dto.workbench.task.TaskNodeDTO;
import com.meituan.csc.aigc.eval.dto.workbench.task.TaskProcessVersionDTO;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.enums.dataset.DataTypeEnum;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetCreateionMethodEnum;
import com.meituan.csc.aigc.eval.enums.dataset.DatasetTypeEnum;
import com.meituan.csc.aigc.eval.enums.es.online.ChatRequestEnum;
import com.meituan.csc.aigc.eval.enums.es.online.EvaluationStarEnum;
import com.meituan.csc.aigc.eval.enums.es.online.QuestionSolveEnum;
import com.meituan.csc.aigc.eval.enums.workbench.*;
import com.meituan.csc.aigc.eval.exception.CheckException;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.exception.workbench.LlmMessageNotFoundException;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.application.ApplicationParam;
import com.meituan.csc.aigc.eval.param.customconfig.LlmSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.NodeSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.SignalReorderParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetConditionParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetDetailConditionParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetExtraParam;
import com.meituan.csc.aigc.eval.param.gpt.ChatGptHttpRequest;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchAgreeParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchCollectParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchDetailParam;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchDetailPathParam;
import com.meituan.csc.aigc.eval.param.mark.AutoInspectionRequestParam;
import com.meituan.csc.aigc.eval.param.task.AidaModelConfig;
import com.meituan.csc.aigc.eval.param.workbench.*;
import com.meituan.csc.aigc.eval.proxy.*;
import com.meituan.csc.aigc.eval.service.AidaExecuteService;
import com.meituan.csc.aigc.eval.service.ApplicationExecuteService;
import com.meituan.csc.aigc.eval.service.ISessionExportService;
import com.meituan.csc.aigc.eval.service.InspectWorkbenchExecuteService;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionCommonService;
import com.meituan.csc.aigc.eval.service.analysis.es.OnlineSessionSearchService;
import com.meituan.csc.aigc.eval.service.helper.OperationAnalysisHelper;
import com.meituan.csc.aigc.eval.service.push.DxPushTextService;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;
import com.meituan.csc.aigc.eval.service.strategy.workbench.WorkbenchQueryStrategyService;
import com.meituan.csc.aigc.eval.utils.*;
import com.meituan.csc.aigc.runtime.common.DialogResultCodeEnum;
import com.meituan.csc.aigc.runtime.common.DialogRoleEnum;
import com.meituan.csc.aigc.runtime.dto.PageResultDTO;
import com.meituan.csc.aigc.runtime.dto.agent.SceneInfoDTO;
import com.meituan.csc.aigc.runtime.dto.aida.AppConfigVersionDTO;
import com.meituan.csc.aigc.runtime.dto.aida.SpaceDTO;
import com.meituan.csc.aigc.runtime.dto.recommendation.DialogChatItem;
import com.meituan.csc.aigc.runtime.dto.recommendation.DialogRequest;
import com.meituan.csc.aigc.runtime.dto.recommendation.DialogResponse;
import com.meituan.csc.aigc.runtime.dto.recommendation.MessageHistoryItem;
import com.meituan.csc.aigc.runtime.dto.workbench.DialogSessionInfoDTO;
import com.meituan.csc.aigc.runtime.dto.workbench.DialogTraceDTO;
import com.meituan.csc.aigc.runtime.dto.workbench.InspectWorkbenchTraceDTO;
import com.meituan.csc.aigc.runtime.enums.workbench.InspectWorkbenchTraceTypeEnum;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.csc.aigc.runtime.param.workbench.DialogSessionConditionParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;
import com.sankuai.csccratos.aida.config.client.dto.eval.*;
import com.sankuai.csccratos.aida.config.client.dto.logInfo.WorkflowNodeExeDetailsDTO;
import com.sankuai.csccratos.aida.config.client.rpc.thrift.EvalRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.common.AidaResponse;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneAppHierarchyDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneTaskHierarchyDTO;
import com.sankuai.csccratos.csc.aida.label.client.enums.SceneTypeEnum;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InspectWorkbenchExecuteServiceImpl implements InspectWorkbenchExecuteService {

    @Autowired
    private PbServiceProxy pbServiceProxy;

    @Autowired
    private SmartInvokeServiceProxy smartInvokeServiceProxy;
    @Autowired
    private IvrInvokeServiceProxy ivrInvokeServiceProxy;

    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;

    @Autowired
    private InspectWorkbenchEvalGeneratorService inspectWorkbenchEvalGeneratorService;

    @Autowired
    private WorkbenchInspectInfoGeneratorService workbenchInspectInfoGeneratorService;

    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;

    @Autowired
    private WorkbenchSessionInfoGeneratorService workbenchSessionInfoGeneratorService;

    @Autowired
    private ApplicationExecuteService applicationExecuteService;

    @Autowired
    private EvalDatasetGeneratorService evalDatasetGeneratorService;

    @Autowired
    private EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;

    @Autowired
    private RedisClientProxy redisClientProxy;

    @Autowired
    private WorkbenchInspectVisitLogGeneratorService workbenchInspectVisitLogGeneratorService;

    @Autowired
    private List<WorkbenchQueryStrategyService> workbenchQueryStrategyServiceList;

    @Autowired
    private AidaMessagesGeneratorService aidaMessagesGeneratorService;

    @Autowired
    private ExcelService excelService;

    @Autowired
    private DxPushTextService dxPushTextService;

    /**
     * session数据导出服务
     */
    @Autowired
    private ISessionExportService sessionExportService;

    @Autowired
    private WorkbenchCustomConfigGeneratorService workbenchCustomConfigGeneratorService;
    @Autowired
    private AidaExecuteService aidaExecuteService;
    @Autowired
    private CommonEvalStrategyService commonEvalStrategyService;
    @Resource
    public RecordQueryServiceProxy recordQueryServiceProxy;

    private Map<Integer, WorkbenchQueryStrategyService> workbenchQueryStrategyServiceMap;

    /**
     * 运营分析工具
     */
    @Autowired
    private OperationAnalysisHelper operationAnalysisHelper;
    @Autowired
    HttpConfig httpConfig;

    private static final List<InspectWorkbenchTraceTypeEnum> DISPLAY_TRACE_TYPE = Lists.newArrayList(InspectWorkbenchTraceTypeEnum.LLM, InspectWorkbenchTraceTypeEnum.INTERFACE, InspectWorkbenchTraceTypeEnum.RULE);

    private static final List<String> DEFAULT_USER_TYPE = Lists.newArrayList("CUSTOMER");

    private static final ExecutorService AIDA_SUB_APPLICATION_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "execute-aida-sub-application-%d");

    /**
     * 工作台导出线程池
     */
    private static final ExecutorService WORKBENCH_SESSION_EXPORT_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "workbench-session-export-thread-%d");

    /**
     * 工作流执行链路查询线程池
     */
    private static final ExecutorService WORKFLOW_LOG_QUERY_POOL = ThreadUtils.createPoolWithTraceAndCat(100, 500, 1000, "workflow-log-query-thread-%d");
    /**
     * 大模型节点类型
     */
    public static final String LLM_NODE_TYPE = "llm";

    /**
     * 开始节点类型
     */
    public static final String START_NODE_TYPE = "start";

    /**
     * 工具节点类型
     */
    private static final String API_NODE_TYPE = "api";

    /**
     * 回复节点类型
     */
    public static final String REPLY_NODE_TYPE = "reply";

    /**
     * 需要转换变量的节点类型
     */
    private static final List<String> NEED_CONVERT_NODE_TYPE = Lists.newArrayList(LLM_NODE_TYPE, API_NODE_TYPE, START_NODE_TYPE, REPLY_NODE_TYPE);

    /**
     * 日志记录线程池
     */
    private static final ExecutorService WORKBENCH_INSPECTION_LOG_THREAD_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "workbench-inspection-log-thread-%d");

    /**
     * Session Id List分隔符号
     */
    public static final String SESSION_ID_LIST_SPLIT = "[ ,，\r\n]";

    @Autowired
    private EvalRemoteService.Iface inspectWorkbenchAidaRemoteService;

    @Autowired
    OnlineSessionCommonService onlineSessionCommonService;
    @Autowired
    OnlineSessionSearchService onlineSessionSearchService;
    @Autowired
    AidaSceneRemoteService aidaSceneRemoteService;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(workbenchQueryStrategyServiceMap)) {
            workbenchQueryStrategyServiceMap = new HashMap<>();
            for (WorkbenchQueryStrategyService workbenchQueryStrategyService : workbenchQueryStrategyServiceList) {
                workbenchQueryStrategyServiceMap.put(workbenchQueryStrategyService.getName(), workbenchQueryStrategyService);
            }
        }
    }

    @Override
    public FilterConfigDTO sessionConditionConfig(String analysisType) {
        // todo add ivr config
        return Lion.getBean(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_ONLINE_FILTER_CONDITION_CONFIG, FilterConfigDTO.class);
    }

    @Override
    public SceneRelationConfigDTO sessionConditionSceneRelationConfig() {
        SceneRelationConfigDTO sceneRelationConfigDTO = new SceneRelationConfigDTO();
        List<SceneRelationConfigDTO.SceneInfo> sceneInfos = Lists.newArrayList();
        // 从aida获取有权限的工作空间/应用/版本
//        List<AidaRobotDTO> aidaRobotDTOList = aidaExecuteService.getAidaRobot();
//        log.info("sessionConditionSceneRelationConfig, aidaExecuteService.getAidaRobot():{}", JSON.toJSONString(aidaRobotDTOList));

        AidaResponse<List<AidaSceneTaskHierarchyDTO>> listAidaSceneTaskHierarchyResponse;
        try {
            listAidaSceneTaskHierarchyResponse = aidaSceneRemoteService.listAllSceneTasksHierarchy();
            log.info("sessionConditionSceneRelationConfig, aidaSceneRemoteService.listAllSceneTasksHierarchy():{}", JSON.toJSONString(listAidaSceneTaskHierarchyResponse));
        } catch (Exception e) {
            log.error("Failed to get scene task hierarchy", e);
            throw new CheckException("获取所有场景的Task相关信息失败");
        }

        AidaResponse<List<AidaSceneAppHierarchyDTO>> listAidaResponse;
        try {
            listAidaResponse = aidaSceneRemoteService.listSceneApplicationsByType(SceneTypeEnum.ONLINE);
            log.info("sessionConditionSceneRelationConfig, aidaSceneRemoteService.listSceneApplicationsByType():{}", JSON.toJSONString(listAidaResponse));
        } catch (Exception e) {
            log.error("Failed to get scene applications hierarchy", e);
            throw new CheckException("获取所有场景的Aida应用信息失败");
        }

        if (listAidaSceneTaskHierarchyResponse.getCode() != 0 || listAidaResponse.getCode() != 0) {
            return sceneRelationConfigDTO;
        }
        // 场景-对应的bu列表
        Map<Long, List<AidaSceneTaskHierarchyDTO.BuInfo>> sceneTaskMap = Maps.newHashMap();
        List<AidaSceneTaskHierarchyDTO> aidaSceneTaskHierarchyDTOList = listAidaSceneTaskHierarchyResponse.getData();
        if (CollectionUtils.isNotEmpty(aidaSceneTaskHierarchyDTOList)) {
            for (AidaSceneTaskHierarchyDTO sceneTaskHierarchyDTO : aidaSceneTaskHierarchyDTOList) {
                sceneTaskMap.put(sceneTaskHierarchyDTO.getSceneId(), sceneTaskHierarchyDTO.getBuInfos());
            }
        }

        // 注意：当下返回的结果没有应用版本信息
        List<AidaSceneAppHierarchyDTO> aidaSceneAppHierarchyDTOList = listAidaResponse.getData();
        if (CollectionUtils.isEmpty(aidaSceneAppHierarchyDTOList)) {
            return sceneRelationConfigDTO;
        }
        fillAidaInfo(aidaSceneAppHierarchyDTOList);

        // 补一下aida应用版本信息
        for (AidaSceneAppHierarchyDTO sceneApplicationDTO : aidaSceneAppHierarchyDTOList) {
            SceneRelationConfigDTO.SceneInfo sceneInfo = new SceneRelationConfigDTO.SceneInfo();
            sceneInfo.setSceneId(sceneApplicationDTO.getSceneId());
            sceneInfo.setSceneName(sceneApplicationDTO.getSceneName());
            sceneInfo.setBuInfos(sceneTaskMap.get(sceneApplicationDTO.getSceneId()));

            List<AidaRobotDTO> targetWorkspaceList = Lists.newArrayList();

            sceneInfo.setWorkspaceAppList(targetWorkspaceList);

            for (SpaceDTO spaceDTO : sceneApplicationDTO.getSpaces()) {
                AidaRobotDTO targetWorkspace = new AidaRobotDTO();
                targetWorkspace.setWorkspaceId(spaceDTO.getSpaceId());
                targetWorkspace.setWorkspaceName(spaceDTO.getSpaceName());
                targetWorkspaceList.add(targetWorkspace);
                if (CollectionUtils.isEmpty(spaceDTO.getApps())) {
                    continue;
                }
                List<AidaRobotDTO.Application> targetApplicationList = Lists.newArrayList();

                for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : spaceDTO.getApps()) {
                    AidaRobotDTO.Application application = new AidaRobotDTO.Application();
                    application.setApplicationId(appDTO.getAppId());
                    application.setApplicationName(appDTO.getAppName());
                    targetApplicationList.add(application);

                    List<AppVersionDTO> appVersionDTOS = aidaInvokeServiceProxy.listAidaAppDtoByIds(appDTO.getAppId());
                    if (CollectionUtils.isEmpty(appVersionDTOS)) {
                        continue;
                    }
                    List<AidaRobotDTO.Robot> robotList = Lists.newArrayList();
                    for (AppVersionDTO appVersionDTO : appVersionDTOS) {
                        AidaRobotDTO.Robot applicationVersion = new AidaRobotDTO.Robot();
                        applicationVersion.setRobotId(appVersionDTO.getVersionId());
                        applicationVersion.setRobotName(appVersionDTO.getVersionName());
                        robotList.add(applicationVersion);
                    }
                    application.setRobotList(robotList);
                }
                targetWorkspace.setApplicationList(targetApplicationList);
            }
            sceneInfos.add(sceneInfo);
        }
        sceneRelationConfigDTO.setSceneInfos(sceneInfos);

        return sceneRelationConfigDTO;
    }

    /**
     * 为场景返回值填充aida信息
     *
     * @param aidaSceneAppHierarchyDTOList
     */
    private void fillAidaInfo(List<AidaSceneAppHierarchyDTO> aidaSceneAppHierarchyDTOList) {
        List<String> sceneSpaceList = Lists.newArrayList();
        List<String> sceneApplicationList = Lists.newArrayList();
        List<String> sceneVersionList = Lists.newArrayList();

        // 获取到场景下的空间和应用列表 场景给到的只有id
        for (AidaSceneAppHierarchyDTO sceneApplicationDTO : aidaSceneAppHierarchyDTOList) {
            List<SpaceDTO> spaces = sceneApplicationDTO.getSpaces();
            if (CollectionUtils.isNotEmpty(spaces)) {
                for (SpaceDTO spaceDTO : spaces) {
                    sceneSpaceList.add(spaceDTO.getSpaceId());
                    List<com.meituan.csc.aigc.runtime.dto.aida.AppDTO> appDTOS = spaceDTO.getApps();
                    if (CollectionUtils.isEmpty(appDTOS)) {
                        continue;
                    }
                    for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : appDTOS) {
                        sceneApplicationList.add(appDTO.getAppId());
                        if (CollectionUtils.isEmpty(appDTO.getAppConfigVersions())) {
                            continue;
                        }
                        for (AppConfigVersionDTO appConfigVersionDTO : appDTO.getAppConfigVersions()) {
                            sceneVersionList.add(appConfigVersionDTO.getId());
                        }
                    }
                }

            }
        }

        List<TenantDTO> sceneTenantDTOSList = aidaInvokeServiceProxy.listAidaTenantDtoByIds(sceneSpaceList);
        List<AppDTO> sceneAppDTOList = aidaInvokeServiceProxy.listAidaAppDtoByIds(sceneApplicationList);

        Map<String, String> workspaceNameMap = sceneTenantDTOSList.stream().collect(Collectors.toMap(TenantDTO::getId, TenantDTO::getName, (a, b) -> a));
        // 更具应用信息构建应用id和应用结构的映射表
        Map<String, AppDTO> appMap = sceneAppDTOList.stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));
        Map<String, String> aidaIdNameMap = appMap.values().stream().collect(Collectors.toMap(AppDTO::getId, AppDTO::getName));

        Map<String, String> appVersionIdNameMap = Maps.newConcurrentMap();
        // todo 批量接口
        for (String versionId : sceneVersionList) {
            VersionConfigDTO versionConfigDTO = aidaInvokeServiceProxy.getVersionConfigByVersionId(versionId);
            if (versionConfigDTO != null) {
                appVersionIdNameMap.put(versionId, versionConfigDTO.getVersionName());
            }
        }

        for (AidaSceneAppHierarchyDTO sceneApplicationDTO : aidaSceneAppHierarchyDTOList) {
            for (SpaceDTO spaceDTO : sceneApplicationDTO.getSpaces()) {
                spaceDTO.setSpaceName(workspaceNameMap.get(spaceDTO.getSpaceId()));
                if (CollectionUtils.isEmpty(spaceDTO.getApps())) {
                    continue;
                }
                for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : spaceDTO.getApps()) {
                    appDTO.setAppName(aidaIdNameMap.get(appDTO.getAppId()));
                    if (CollectionUtils.isEmpty(appDTO.getAppConfigVersions())) {
                        continue;
                    }
                    for (AppConfigVersionDTO appConfigVersionDTO : appDTO.getAppConfigVersions()) {
                        appConfigVersionDTO.setName(appVersionIdNameMap.get(appConfigVersionDTO.getId()));
                    }
                }
            }
        }
    }

    @Override
    public TaskVersionDTO sessionConditionTaskKeyVersions(String taskKey) {
        TaskVersionDTO taskVersionDTO = new TaskVersionDTO();
        taskVersionDTO.setTaskKey(taskKey);
        CommonUtils.checkEval(StringUtils.isNotBlank(taskKey), "taskKey不能为空");

        // 返回task的版本列表
        List<TaskProcessVersionDTO> taskVersionList = onlineSessionCommonService.listTaskVersionByTaskKey(taskKey);
        taskVersionDTO.setTaskVersions(taskVersionList);
        return taskVersionDTO;
    }

    @Override
    public TaskVersionNodeDTO sessionConditionTaskKeyNodes(String taskKey, String taskVersion) {
        TaskVersionNodeDTO taskInfoConfigDTO = new TaskVersionNodeDTO();
        taskInfoConfigDTO.setTaskKey(taskKey);
        taskInfoConfigDTO.setTaskVersion(taskVersion);
        CommonUtils.checkEval(StringUtils.isNotBlank(taskKey), "taskKey不能为空");
        CommonUtils.checkEval(StringUtils.isNotBlank(taskVersion), "taskVersion不能为空");

        // 返回taskKey和taskVersion对应的taskNodes
        List<TaskNodeDTO> taskNodeList = onlineSessionCommonService.listTaskNodeByTaskKeyAndVersion(taskKey, Integer.valueOf(taskVersion));
        taskInfoConfigDTO.setTaskVersionNodes(taskNodeList);

        return taskInfoConfigDTO;
    }

    @Override
    public boolean addSessionConditionRecord(SessionConditionRecordDTO param) {
        // 1. 重名检查
        String userMis = UserUtils.getUser().getLogin();
        WorkbenchCustomConfigCondition condition = new WorkbenchCustomConfigCondition();
        condition.setCreatorMis(userMis);
        condition.setName(param.getRecordName());
        String type = ChannelEnum.ONLINE.getCode().equals(param.getAnalysisType()) ? CustomConfigTypeEnum.WORKBENCH_SESSION_ONLINE_CONDITION_CONFIG.getCode() : CustomConfigTypeEnum.WORKBENCH_SESSION_IVR_CONDITION_CONFIG.getCode();
        condition.setType(type);
        List<WorkbenchCustomConfigPo> recordPoList = workbenchCustomConfigGeneratorService.listByCondition(condition);
        CommonUtils.checkEval(CollectionUtils.isEmpty(recordPoList), "条件名称已存在，请重新输入");

        // 2. 存储
        Date date = new Date();
        WorkbenchCustomConfigPo customConfigPo = new WorkbenchCustomConfigPo();
        customConfigPo.setType(type);
        customConfigPo.setName(param.getRecordName());
        customConfigPo.setConfig(JSON.toJSONString(param.getConditionRecords()));
        customConfigPo.setCreatorMis(userMis);
        customConfigPo.setUpdaterMis(userMis);
        customConfigPo.setGmtCreated(date);
        customConfigPo.setGmtModified(date);
        customConfigPo.setPlatformWorkspace("");
        customConfigPo.setStatus(CustomConfigStatusEnum.VAILD.getCode());
        return workbenchCustomConfigGeneratorService.save(customConfigPo);
    }

    @Override
    public List<SessionConditionRecordDTO> listSessionConditionRecord(String analysisType) {
        String userMis = UserUtils.getUser().getLogin();
        WorkbenchCustomConfigCondition condition = new WorkbenchCustomConfigCondition();
        condition.setCreatorMis(userMis);
        String type = ChannelEnum.ONLINE.getCode().equals(analysisType) ? CustomConfigTypeEnum.WORKBENCH_SESSION_ONLINE_CONDITION_CONFIG.getCode() : CustomConfigTypeEnum.WORKBENCH_SESSION_IVR_CONDITION_CONFIG.getCode();
        condition.setType(type);
        condition.setStatus(CustomConfigStatusEnum.VAILD.getCode());
        List<WorkbenchCustomConfigPo> customConfigPoList = workbenchCustomConfigGeneratorService.listByCondition(condition);

        List<SessionConditionRecordDTO> recordDTOList = Lists.newArrayList();
        for (WorkbenchCustomConfigPo configPo : customConfigPoList) {
            SessionConditionRecordDTO recordDTO = new SessionConditionRecordDTO();
            recordDTO.setRecordId(configPo.getId());
            recordDTO.setRecordName(configPo.getName());
            recordDTO.setAnalysisType(configPo.getType());
            recordDTO.setConditionRecords(JSON.parseArray(configPo.getConfig(), FilterConditionRecordDTO.class));
            recordDTO.setCreateTime(configPo.getGmtCreated());
            recordDTOList.add(recordDTO);
        }
        return recordDTOList;
    }

    @Override
    public boolean deleteSessionConditionRecord(SessionConditionRecordDTO param) {
        WorkbenchCustomConfigPo configPo = workbenchCustomConfigGeneratorService.getById(param.getRecordId());
        if (configPo == null) {
            return true;
        }

        configPo.setStatus(CustomConfigStatusEnum.INVALID.getCode());
        configPo.setUpdaterMis(UserUtils.getUser().getLogin());
        configPo.setGmtModified(new Date());
        return workbenchCustomConfigGeneratorService.updateById(configPo);
    }

    /**
     * ES查询在线会话
     *
     * @param param
     * @return
     */
    private PageData<AidaOnlineSessionEsDTO> searchOlineSessionFromES(PageParam<InspectWorkbenchConditionParam> param) {
        OnlineSessionSearchDTO searchDTO = new OnlineSessionSearchDTO();
        if (StringUtils.isNotBlank(param.getCondition().getSessionId())) {
            List<String> sessionIdList = splitString(param.getCondition().getSessionId());
            searchDTO.setSessionIdList(sessionIdList);
        }
        if (null != param.getCondition().getStartTime()) {
            searchDTO.setStartTime(param.getCondition().getStartTime().getTime());
        }
        if (null != param.getCondition().getEndTime()) {
            searchDTO.setEndTime(param.getCondition().getEndTime().getTime());
        }

        searchDTO.setPageNo(param.getPageNum());
        searchDTO.setPageSize(param.getPageSize());
        searchDTO.setScene(param.getCondition().getScene());
        searchDTO.setBu(param.getCondition().getBu());
        searchDTO.setSubBu(param.getCondition().getSubBu());
        searchDTO.setSpaceId(param.getCondition().getWorkspaceId());
        searchDTO.setAppId(param.getCondition().getApplicationId());
        searchDTO.setVersionId(param.getCondition().getApplicationVersionId());
        searchDTO.setIsExceptionEnd(param.getCondition().getIsExceptionEnd());
        searchDTO.setIsTimeoutEnd(param.getCondition().getIsTimeoutEnd());
        searchDTO.setQuestionName(param.getCondition().getQuestionName());
        searchDTO.setSessionSolved(param.getCondition().getSessionSolved());
        searchDTO.setStars(StringUtils.isNotBlank(param.getCondition().getStars()) ? Lists.newArrayList(param.getCondition().getStars()) : Lists.newArrayList());
        searchDTO.setTransferStaff(param.getCondition().getTransferStaff());
        searchDTO.setVisitId(param.getCondition().getVisitId());
        searchDTO.setTaskKey(param.getCondition().getTaskKey());
        searchDTO.setTaskVersion(param.getCondition().getTaskVersion());
        searchDTO.setTaskNodeId(param.getCondition().getTaskNode());
        PageData<AidaOnlineSessionEsDTO> esPageData = onlineSessionSearchService.pageSessions(searchDTO);
        log.info("searchOlineSessionFromES, searchDTO:{}, esPageData:{}", JSON.toJSONString(searchDTO), JSON.toJSONString(esPageData));
        return esPageData;
    }

    /**
     * 获取场景下aida应用列表
     *
     * @param sceneTypeEnum
     * @return
     */
    private List<String> findSceneApplicationListByType(SceneTypeEnum sceneTypeEnum) {
        // 注意：从场景去取aida应用信息，es里面无法区分入口agent
        List<String> sceneApplicationList = Lists.newArrayList();
        try {
            AidaResponse<List<AidaSceneAppHierarchyDTO>> listAidaResponse = aidaSceneRemoteService.listSceneApplicationsByType(sceneTypeEnum);
            if (listAidaResponse.getCode() == 0 && CollectionUtils.isNotEmpty(listAidaResponse.getData())) {
                for (AidaSceneAppHierarchyDTO sceneApplicationDTO : listAidaResponse.getData()) {
                    List<SpaceDTO> spaces = sceneApplicationDTO.getSpaces();
                    if (CollectionUtils.isNotEmpty(spaces)) {
                        for (SpaceDTO spaceDTO : spaces) {
                            List<com.meituan.csc.aigc.runtime.dto.aida.AppDTO> appDTOS = spaceDTO.getApps();
                            if (CollectionUtils.isEmpty(appDTOS)) {
                                continue;
                            }
                            for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : appDTOS) {
                                sceneApplicationList.add(appDTO.getAppId());
                            }
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.error("获取场景应用信息失败,sceneTypeEnum={},msg={}", sceneTypeEnum, e.getMessage(), e);
        }
        return sceneApplicationList;
    }

    /**
     * ES查询结果转换为InspectWorkbenchDTO
     *
     * @param esPageData
     * @return
     */
    private PageData<InspectWorkbenchDTO> convertTo(PageData<AidaOnlineSessionEsDTO> esPageData) {
        if (esPageData == null || CollectionUtils.isEmpty(esPageData.getData())) {
            return PageData.emptyData(esPageData.getPageNum(), esPageData.getPageSize());
        }

        List<String> sceneApplicationList = findSceneApplicationListByType(SceneTypeEnum.ONLINE);
        Set<String> workspaceIdSet = Sets.newHashSet();
        Set<String> applicationIdSet = Sets.newHashSet();
        Set<String> versionIdSet = Sets.newHashSet();
        for (AidaOnlineSessionEsDTO esDTO : esPageData.getData()) {
            if (CollectionUtils.isNotEmpty(esDTO.getAidaAppInfo())) {
                for (AidaOnlineSessionEsDTO.AidaAppInfo aidaAppInfo : esDTO.getAidaAppInfo()) {
                    // 取交集
                    if (CollectionUtils.isNotEmpty(sceneApplicationList) && sceneApplicationList.contains(aidaAppInfo.getAppId())) {
                        workspaceIdSet.add(aidaAppInfo.getSpaceId());
                        applicationIdSet.add(aidaAppInfo.getAppId());
                        versionIdSet.add(aidaAppInfo.getVersionId());
                    }
                }
            }
        }
        List<String> workspaceIdList = new ArrayList<>(workspaceIdSet);
        List<String> applicationIdList = new ArrayList<>(applicationIdSet);
        List<String> versionIdList = new ArrayList<>(versionIdSet);
        // 根据appId列表查询出app信息
        List<AppDTO> apps = aidaInvokeServiceProxy.listAidaAppDtoByIds(applicationIdList);
        // 根据workspaceId列表查询出workspace信息
        List<TenantDTO> tenantDTOS = aidaInvokeServiceProxy.listAidaTenantDtoByIds(workspaceIdList);
        Map<String, String> workspaceNameMap = tenantDTOS.stream().collect(Collectors.toMap(TenantDTO::getId, TenantDTO::getName, (a, b) -> a));
        // 更具应用信息构建应用id和应用结构的映射表
        Map<String, AppDTO> appMap = apps.stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));
        Map<String, String> appVersionIdNameMap = Maps.newConcurrentMap();
        // todo 批量接口
        for (String versionId : versionIdList) {
            VersionConfigDTO versionConfigDTO = aidaInvokeServiceProxy.getVersionConfigByVersionId(versionId);
            if (versionConfigDTO != null) {
                appVersionIdNameMap.put(versionId, versionConfigDTO.getVersionName());
            }
        }
        List<InspectWorkbenchDTO> resultList = Lists.newArrayList();
        Map<Long, String> aidaSceneIdNameMap = findSceneIdNameMap();
        Boolean queryMessageCount = Lion.getBoolean(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_QUERY_MESSAGE_COUNT, true);
        for (AidaOnlineSessionEsDTO esDTO : esPageData.getData()) {
            InspectWorkbenchDTO workbenchDTO = new InspectWorkbenchDTO();
            fillAllFieldInfo(workbenchDTO, esDTO, aidaSceneIdNameMap, appMap, workspaceNameMap, appVersionIdNameMap);

            if (queryMessageCount != null && queryMessageCount) {
                workbenchDTO.setMessageCount(operationAnalysisHelper.getOnlineSessionMessageCount(esDTO.getSessionId(), esDTO.getUserType(), esDTO.getUserId()));
            }
            resultList.add(workbenchDTO);
        }

        return PageData.create(esPageData.getTotalNum(), esPageData.getPageNum(), esPageData.getPageSize(), resultList);
    }

    /**
     * 为db会话填充字段
     *
     * @param workbenchDTO
     * @param esDTO
     */
    private void fillSessionField(InspectWorkbenchDTO workbenchDTO, AidaOnlineSessionEsDTO esDTO, Map<Long, String> aidaSceneIdNameMap) {
        workbenchDTO.setTransferStaff(parseTransferStaffType(esDTO.getTypeList(), esDTO.getSenderTypeList()));
        // 设置满意度
        if (StringUtils.isNotBlank(esDTO.getStars())) {
            workbenchDTO.setStars(EvaluationStarEnum.getDescByCode(esDTO.getStars()));
        } else {
            workbenchDTO.setStars(EvaluationStarEnum.NO_STARS.getDesc());
        }

        // 设置问题是否已解决
        if (StringUtils.isNotBlank(esDTO.getSessionSolved())) {
            workbenchDTO.setSessionSolved(QuestionSolveEnum.getDescByCode(esDTO.getSessionSolved()));
        } else {
            workbenchDTO.setSessionSolved(QuestionSolveEnum.NO_EVALUATE.getDesc());
        }

        // 设置转人工信息
        workbenchDTO.setTransferStaff(parseTransferStaffType(esDTO.getTypeList(), esDTO.getSenderTypeList()));

        List<String> esSceneList = esDTO.getBizSceneId();
        // 将场景ID转换为场景名称列表
        List<String> sceneNameList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(esSceneList)) {
            for (String sceneId : esSceneList) {
                String sceneName = aidaSceneIdNameMap.get(Long.parseLong(sceneId));
                if (sceneName != null) {
                    sceneNameList.add(sceneName);
                }
            }
            // 标问
            workbenchDTO.setSceneName(String.join(",", sceneNameList));
            workbenchDTO.setStandardQuestion(parseQuestion(esDTO.getRobotInvokeLogs()));
        }
    }

    private void fillAllFieldInfo(InspectWorkbenchDTO workbenchDTO, AidaOnlineSessionEsDTO esDTO, Map<Long, String> aidaSceneIdNameMap,
                                  Map<String, AppDTO> appMap, Map<String, String> workspaceNameMap, Map<String, String> appVersionIdNameMap) {
        // 设置基本信息
        workbenchDTO.setVisitIds(String.join(",", esDTO.getVisitIdList()));
        workbenchDTO.setSessionId(esDTO.getSessionId());
        workbenchDTO.setUserId(esDTO.getUserId());
        workbenchDTO.setUserType(esDTO.getUserType());
        workbenchDTO.setOrderId(esDTO.getOrderId());
        workbenchDTO.setTime(esDTO.getCreateTime());

        // 设置渠道信息
        if (CollectionUtils.isNotEmpty(esDTO.getChannelList())) {
            workbenchDTO.setChannel(String.join(",", esDTO.getChannelList()));
        }

        // 设置应用信息
        if (CollectionUtils.isNotEmpty(esDTO.getAidaAppInfo()) && CollectionUtils.isEmpty(workbenchDTO.getWorkspaceAppInfo())) {
            Pair<Map<String, Set<String>>, Map<String, Set<String>>> mappings = operationAnalysisHelper.buildAppMappingsWithEs(esDTO.getAidaAppInfo(), appMap, workspaceNameMap);
            List<WorkspaceAppDTO> workspaceAppList = operationAnalysisHelper.buildWorkspaceAppList(mappings.getLeft(), mappings.getRight(), workspaceNameMap, appMap, appVersionIdNameMap);
            workbenchDTO.setWorkspaceAppInfo(workspaceAppList);
        }
        fillSessionField(workbenchDTO, esDTO, aidaSceneIdNameMap);

    }

    /**
     * 标问名称转换
     *
     * @param robotInvokeLogs 标问机器人执行记录
     * @return 标问名称, 分割
     */
    private String parseQuestion(List<AidaOnlineSessionEsDTO.RobotInvokeLog> robotInvokeLogs) {
        if (CollectionUtils.isEmpty(robotInvokeLogs)) {
            return null;
        }
        return robotInvokeLogs.stream()
                .filter(e -> StringUtils.isNotBlank(e.getTypicalQuestionId()) && StringUtils.isNotBlank(e.getTypicalQuestionName()))
                .map(AidaOnlineSessionEsDTO.RobotInvokeLog::getTypicalQuestionName)
                .distinct()
                .collect(Collectors.joining(","));
    }

    /**
     * 转人工类型转换
     *
     * @param typeList       消息类型
     * @param senderTypeList 发送方类型
     * @return 转人工类型描述
     */
    private String parseTransferStaffType(List<String> typeList, List<String> senderTypeList) {
        // 虚拟客服不属于转人工范畴
        if (CollectionUtils.isNotEmpty(senderTypeList) && senderTypeList.contains("VIRTUAL_KEFU") && !senderTypeList.contains("STAFF")) {
            return ChatRequestEnum.CHAT_REQUEST_NO.getDesc();
        }
        if (CollectionUtils.isEmpty(typeList)) {
            return ChatRequestEnum.CHAT_REQUEST_NO.getDesc();
        }
        if (typeList.contains(ChatRequestEnum.CHAT_REQUEST_YES.getCode())) {
            if (typeList.contains(ChatRequestEnum.CHAT_BEGIN_YES.getCode())) {
                ChatRequestEnum chatRequestDetailFlag = getChatRequestDetailFlag(senderTypeList);
                return chatRequestDetailFlag.getDesc();
            } else {
                return ChatRequestEnum.CHAT_BEGIN_NO.getDesc();
            }
        }
        return ChatRequestEnum.CHAT_REQUEST_NO.getDesc();
    }

    /**
     * 获取转人工类型详情
     *
     * @param senderTypeList 发送方类型
     * @return 转人工类型枚举
     */
    private ChatRequestEnum getChatRequestDetailFlag(List<String> senderTypeList) {
        if (CollectionUtils.isNotEmpty(senderTypeList)) {
            if (senderTypeList.contains("STAFF")) {
                // 转坐席
                return ChatRequestEnum.CHAT_BEGIN_YES;
            } else if (senderTypeList.contains("WAIMAI_SHOP_BD") || senderTypeList.contains("WAIMAI_SHOP_SYSTEM")) {
                // 转BD
                return ChatRequestEnum.CHAT_BEGIN_BD;
            } else if (senderTypeList.contains("STAFF_ROBOT")) {
                // 托管机器人
                return ChatRequestEnum.CHAT_BEGIN_STAFF_ROBOT;
            } else if (senderTypeList.contains("VIRTUAL_KEFU")) {
                // 虚拟客服
                return ChatRequestEnum.CHAT_BEGIN_VIRTUAL_KEFU;
            }
        }
        // 默认转坐席
        return ChatRequestEnum.CHAT_BEGIN_YES;
    }

    /**
     * 补全服务过程信息
     *
     * @param sessionId
     * @param addTime
     * @param workbenchDTO
     */
    private void appendServiceProcessInfo(String sessionId, Long addTime, InspectWorkbenchDTO workbenchDTO) {
        String urlPrefix = Lion.getString(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_FUYAO_SERVICE_PROCESS_URL, "https://aics.sankuai.com");

        String urlFormat = "%s/api/portal/sessionAnalysis/getServiceProcessInfo?sessionId=%s&addTime=%s";
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, "application/json");
        String url = String.format(urlFormat, urlPrefix, sessionId, addTime);

        try {
            long start = System.currentTimeMillis();
//            url = "https://aics.sankuai.com/api/portal/sessionAnalysis/getServiceProcessInfo?addTime=1746788912000&sessionId=1920798032912883740";
            String response = HttpUtil.sendGet(httpHeaders, url, String.class, httpConfig.getRestTemplate());
            log.info("获取会话服务信息API返回结果，response={}, request={}, cost={}", response, url, System.currentTimeMillis() - start);
//            String response = mockServiceProcessInfo();
            if (StringUtils.isNotEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (HttpStatus.OK.value() == jsonObject.getInteger("code")) {
//                    ServiceProcessInfo serviceProcessInfo = JSON.parseObject(jsonObject.getString("data"), ServiceProcessInfo.class);
//                    if (serviceProcessInfo != null) {
                    workbenchDTO.setServiceProcessInfo(jsonObject.getString("data"));
//                    }
                }
            }
        } catch (Exception e) {
            log.error("appendServiceProcessInfo, sessionId:{}, addTime:{}, workbenchDTO:{}", sessionId, addTime, JSON.toJSONString(workbenchDTO), e);
        }
    }

    private String mockServiceProcessInfo() {

        String result = "{\"data\":{\"sessionId\":\"1920798032912883740\",\"startTime\":\"2025-05-09 19:08:32\",\"groups\":[{\"itemsSize\":1,\"serviceProcessType\":\"doufukuai\",\"serviceProcessName\":\"豆腐块\",\"triggerTime\":\"2025-05-09 19:08:32\",\"items\":[{\"uuid\":\"7807cab1-fa62-4546-b348-e8d320bf832e\",\"serviceProcessType\":\"doufukuai\",\"serviceProcessName\":\"豆腐块\",\"triggerTime\":\"2025-05-09 19:08:32\",\"endTime\":\"2025-05-09 19:08:32\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"clickTypicalQuestionId\":124,\"clickTypicalQuestionName\":\"我要申请退款\",\"jumpOpen\":false}}]},{\"itemsSize\":1,\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:08:32\",\"items\":[{\"uuid\":\"21f211bc-0c8b-45a8-892f-adb0fada72a4\",\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:08:32\",\"endTime\":\"2025-05-09 19:08:33\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"noticeId\":4284,\"recommendId\":199,\"name\":\"温馨提示~\",\"content\":\"<p>尊敬的用户，您好！当前运力紧张，可能会出现无骑手接单、配送延迟的情况。但请您放心，商家和骑手团队正全力以赴地制作与配送。希望您能多给予一些耐心，<strong style=\\\"color: rgb(255, 153, 0);\\\">感谢您的理解与支持，祝您用餐愉快！</strong></p>\",\"rules\":[{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"id\":2097,\"name\":\"【峰值公告】峰值等级-繁忙\",\"serviceRobot\":true}],\"applySceneId\":6,\"applySceneName\":\"公告策略\"},\"messageIdList\":[\"N4284_1746788912807\"]}]},{\"itemsSize\":3,\"serviceProcessType\":\"light\",\"serviceProcessName\":\"轻交互\",\"triggerTime\":\"2025-05-09 19:08:33\",\"items\":[{\"uuid\":\"13100b61-883d-40f7-a2ee-59663b38a976\",\"serviceProcessType\":\"light\",\"serviceProcessName\":\"轻交互\",\"triggerTime\":\"2025-05-09 19:08:33\",\"endTime\":\"2025-05-09 19:08:33\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"userId\":\"1792186927\",\"userType\":\"11\",\"sessionId\":\"1920798032912883740\",\"visitId\":\"access-6f0370e6-5262-499f-958f-26f6cbc5e386\",\"appKey\":\"waimai_waimai_mtWaimaiApp\",\"buId\":\"10\",\"subBuId\":\"8\",\"buName\":\"外卖\",\"subBuName\":\"外卖订单问题\",\"appName\":\"美团外卖APP\",\"appSource\":\"MT_WAIMAI_APP\",\"pageName\":\"轻交互页面\",\"pageType\":\"H5-轻交互H5版本\",\"eventName\":\"公告弹窗曝光\",\"positionName\":\"公告区\",\"orderId\":\"2501590900571241022\",\"title\":\"温馨提示~\",\"noticeId\":\"4284\",\"noticeBuId\":\"10\",\"triggerTime\":1746788913480,\"dataFlag\":\"b_cs_smart_portal_wjoyu5pr_mv\",\"timePosition\":\"left\",\"lightTemplateId\":1394,\"lightTemplateName\":\"美团外卖APP订单页-订单卡配送推荐实验\",\"lightModuleName\":\"公告弹窗曝光\",\"lightOperationName\":\"温馨提示~ (4284)\"}},{\"uuid\":\"d5a7298d-4446-426e-841b-ceb13436f7e8\",\"serviceProcessType\":\"light\",\"serviceProcessName\":\"轻交互\",\"triggerTime\":\"2025-05-09 19:08:33\",\"endTime\":\"2025-05-09 19:08:45\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"userId\":\"1792186927\",\"userType\":\"11\",\"sessionId\":\"1920798032912883740\",\"visitId\":\"access-6f0370e6-5262-499f-958f-26f6cbc5e386\",\"appKey\":\"waimai_waimai_mtWaimaiApp\",\"buId\":\"10\",\"subBuId\":\"8\",\"buName\":\"外卖\",\"subBuName\":\"外卖订单问题\",\"appName\":\"美团外卖APP\",\"appSource\":\"MT_WAIMAI_APP\",\"pageName\":\"轻交互页面\",\"pageType\":\"H5-轻交互H5版本\",\"eventName\":\"列表选择区曝光\",\"positionName\":\"列表选择区\",\"orderId\":\"2501590900571241022\",\"triggerTime\":1746788913481,\"dataFlag\":\"b_cs_smart_portal_ncpia5e1_mv\",\"timePosition\":\"right\",\"lightTemplateId\":1394,\"lightTemplateName\":\"美团外卖APP订单页-订单卡配送推荐实验\",\"lightModuleName\":\"列表选择\",\"lightOperationName\":\"默认: 2501590900571241022\"}},{\"uuid\":\"3f939a8d-c4e1-4f03-b640-52e3599f6466\",\"serviceProcessType\":\"light\",\"serviceProcessName\":\"轻交互\",\"triggerTime\":\"2025-05-09 19:08:45\",\"endTime\":\"2025-05-09 19:08:46\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"userId\":\"1792186927\",\"userType\":\"11\",\"sessionId\":\"1920798032912883740\",\"visitId\":\"access-6f0370e6-5262-499f-958f-26f6cbc5e386\",\"appKey\":\"waimai_waimai_mtWaimaiApp\",\"buId\":\"10\",\"subBuId\":\"8\",\"buName\":\"外卖\",\"subBuName\":\"外卖订单问题\",\"appName\":\"美团外卖APP\",\"appSource\":\"MT_WAIMAI_APP\",\"pageName\":\"轻交互页面\",\"pageType\":\"H5-轻交互H5版本\",\"eventName\":\"点击热门问题选项\",\"positionName\":\"热门问题区\",\"orderId\":\"2501590900571241022\",\"tabName\":\"帮助中心\",\"operationRank\":\"1\",\"typicalQuestionId\":\"124\",\"typicalQuestionName\":\"我要申请退款\",\"jumpType\":\"portal\",\"jumpLink\":\"https://h5.dianping.com/app/cs-fe-mai-portal/index.html#/chat?visitId=access-6f0370e6-5262-499f-958f-26f6cbc5e386&subSource=MT_WAIMAI_APP&bu=waimai&template=waimai_test_gray&themeType=0&isLogin=true&csAbTest=VKefu-0%2Centry-3&pragmaEnv=gray-release-service-u1&__end&ft=5&typicalQuestionId=124&query=%E6%88%91%E8%A6%81%E7%94%B3%E8%AF%B7%E9%80%80%E6%AC%BE\",\"triggerTime\":1746788925244,\"dataFlag\":\"b_cs_smart_portal_9gyv32vo_mc\",\"timePosition\":\"right\",\"lightTemplateId\":1394,\"lightTemplateName\":\"美团外卖APP订单页-订单卡配送推荐实验\",\"lightModuleName\":\"猜你想问\",\"lightOperationName\":\"点击我要申请退款 (portal)\"}}]},{\"itemsSize\":1,\"serviceProcessType\":\"robotInvoke\",\"serviceProcessName\":\"用户问题及解决方案\",\"triggerTime\":\"2025-05-09 19:08:46\",\"items\":[{\"uuid\":\"07c6b7f8-c701-4dcd-8dd1-bd7d7cb8ba98\",\"serviceProcessType\":\"robotInvoke\",\"serviceProcessName\":\"用户问题及解决方案\",\"triggerTime\":\"2025-05-09 19:08:46\",\"endTime\":\"2025-05-09 19:08:46\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"userInput\":\"我要申请退款\",\"userInputType\":\"用户点选标准问\",\"answerLevel\":\"HIGH\",\"typicalQuestionId\":124,\"typicalQuestionName\":\"我要申请退款\",\"orderId\":\"2501590900571241022\",\"orderStatus\":\"已申请退款\",\"solutionType\":\"taskSolution\",\"solution\":{\"taskKey\":\"PID_waimai_shenQingTuiKuanV4_Oxx\",\"taskName\":\"申请退款V4-金辉版本\",\"taskType\":0,\"taskInstanceId\":\"e6a32b7d-1e05-4cc6-b4cc-a5c7195d557b\",\"taskTag\":\"正常完成\",\"taskChannel\":\"online\",\"relationTasks\":[{\"taskKey\":\"PID_waimai_BaiMingDanLvYueQuanLianLu_Veu\",\"taskName\":\"【白名单】履约全链路(子)\",\"taskType\":1,\"taskInstanceId\":\"bb300595-3cec-43fa-b428-7e70b3c6de28\",\"triggerTime\":\"2025-05-09 19:08:47\"},{\"taskKey\":\"PID_waimai_pinPaiidPanDuan\",\"taskName\":\"品牌id判断(子)\",\"taskType\":1,\"taskInstanceId\":\"0565bb8c-eb11-4b3a-bd56-e07869995a2a\",\"triggerTime\":\"2025-05-09 19:08:48\"},{\"taskKey\":\"PID_waimai_QuanChangJingDaMoXing_EEk\",\"taskName\":\"【全场景】大模型\",\"taskType\":0,\"taskInstanceId\":\"7a47c2cb-3aad-47a8-8601-583cf95083b8\",\"taskTag\":\"超时退出\",\"taskChannel\":\"online\",\"solutionType\":\"taskSolution\",\"solutionName\":\"Task答案\",\"triggerTime\":\"2025-05-09 19:08:49\"},{\"taskKey\":\"PID_waimai_dingDanXinXiLiQingZitask_VJO\",\"taskName\":\"【退款】大模型准入条件(子)\",\"taskType\":1,\"taskInstanceId\":\"8cfeda65-b4d1-4d68-9ae8-e84a57db7e96\",\"triggerTime\":\"2025-05-09 19:08:49\"}],\"relationTaskList\":[[{\"taskKey\":\"PID_waimai_BaiMingDanLvYueQuanLianLu_Veu\",\"taskName\":\"【白名单】履约全链路(子)\",\"taskType\":1,\"taskInstanceId\":\"bb300595-3cec-43fa-b428-7e70b3c6de28\",\"triggerTime\":\"2025-05-09 19:08:47\"},{\"taskKey\":\"PID_waimai_pinPaiidPanDuan\",\"taskName\":\"品牌id判断(子)\",\"taskType\":1,\"taskInstanceId\":\"0565bb8c-eb11-4b3a-bd56-e07869995a2a\",\"triggerTime\":\"2025-05-09 19:08:48\"},{\"taskKey\":\"PID_waimai_QuanChangJingDaMoXing_EEk\",\"taskName\":\"【全场景】大模型\",\"taskType\":0,\"taskInstanceId\":\"7a47c2cb-3aad-47a8-8601-583cf95083b8\",\"taskTag\":\"超时退出\",\"taskChannel\":\"online\",\"solutionType\":\"taskSolution\",\"solutionName\":\"Task答案\",\"triggerTime\":\"2025-05-09 19:08:49\"},{\"taskKey\":\"PID_waimai_dingDanXinXiLiQingZitask_VJO\",\"taskName\":\"【退款】大模型准入条件(子)\",\"taskType\":1,\"taskInstanceId\":\"8cfeda65-b4d1-4d68-9ae8-e84a57db7e96\",\"triggerTime\":\"2025-05-09 19:08:49\"}]],\"solutionType\":\"taskSolution\",\"solutionName\":\"Task答案\"},\"orderList\":[{\"orderId\":\"2501590900571241022\",\"orderStatus\":\"已申请退款\",\"triggerTime\":1746788926000}],\"tagCodeList\":[\"smartChatOrderDiff\",\"smartChatOrderDiff\"]},\"messageIdList\":[\"1920798092677525587\"]}]},{\"itemsSize\":1,\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:08:46\",\"items\":[{\"uuid\":\"6301e9da-c806-4772-bd34-7c676d73a386\",\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:08:46\",\"endTime\":\"2025-05-09 19:08:58\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"noticeId\":4284,\"recommendId\":199,\"name\":\"温馨提示~\",\"content\":\"<p>尊敬的用户，您好！当前运力紧张，可能会出现无骑手接单、配送延迟的情况。但请您放心，商家和骑手团队正全力以赴地制作与配送。希望您能多给予一些耐心，<strong style=\\\"color: rgb(255, 153, 0);\\\">感谢您的理解与支持，祝您用餐愉快！</strong></p>\",\"rules\":[{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"id\":2097,\"name\":\"【峰值公告】峰值等级-繁忙\",\"serviceRobot\":true}],\"applySceneId\":6,\"applySceneName\":\"公告策略\"},\"messageIdList\":[\"N4284_1746788926254\"]}]},{\"itemsSize\":1,\"serviceProcessType\":\"transfer\",\"serviceProcessName\":\"转人工\",\"triggerTime\":\"2025-05-09 19:08:58\",\"items\":[{\"uuid\":\"7a316eb8-9813-434b-81ce-49145254f0e0\",\"serviceProcessType\":\"transfer\",\"serviceProcessName\":\"转人工\",\"triggerTime\":\"2025-05-09 19:08:58\",\"endTime\":\"2025-05-09 19:11:04\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"strategyId\":\"493\",\"strategyName\":\"入口展示规则-兜底规则\",\"sceneType\":\"入口展示规则-兜底规则\",\"transferType\":\"用户转人工\",\"rules\":[{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"id\":4,\"name\":\"兜底规则\",\"serviceRobot\":true}],\"chatId\":\"621119039\",\"status\":\"已接入人工客服\",\"skillId\":277,\"skillName\":\"外卖客服_在线_初级技能\",\"caseId\":\"2012312106\",\"faqQuestion\":\"外卖.外卖订单问题.外卖.个人原因不想要.个人原因不想要.计划有变不想要\",\"question\":\"计划有变不想要\",\"skillChangeList\":[{\"skillId\":277,\"skillName\":\"外卖客服_在线_初级技能\",\"time\":1746788937372,\"hitTime\":\"2025-05-09 19:08:57\",\"reason\":\"门户左下角转人工\"},{\"skillId\":277,\"skillName\":\"外卖客服_在线_初级技能\",\"time\":1746788937374,\"hitTime\":\"2025-05-09 19:08:57\",\"reason\":\"入口展示规则技能组\"}],\"orderDetailList\":[{\"orderId\":\"2501590900571241022\",\"orderType\":\"noSure\",\"orderStatus\":\"已申请退款\",\"orderTime\":1746788974000,\"orderStage\":\"chat\"}],\"transferApplyInfo\":\"在线+电话技能组(471)\",\"channelTemplateId\":471,\"channelTemplateName\":\"在线+电话技能组\"},\"messageIdList\":[\"1920798139100459061\",\"1920798156418740228\"]}]},{\"itemsSize\":1,\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:11:04\",\"items\":[{\"uuid\":\"484fc768-4f1e-44cf-a34e-908886244c40\",\"serviceProcessType\":\"notice\",\"serviceProcessName\":\"公告\",\"triggerTime\":\"2025-05-09 19:11:04\",\"businessInfo\":{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"serviceRobot\":true},\"displayLevel\":\"HIGH\",\"detail\":{\"noticeId\":4284,\"recommendId\":199,\"name\":\"温馨提示~\",\"content\":\"<p>尊敬的用户，您好！当前运力紧张，可能会出现无骑手接单、配送延迟的情况。但请您放心，商家和骑手团队正全力以赴地制作与配送。希望您能多给予一些耐心，<strong style=\\\"color: rgb(255, 153, 0);\\\">感谢您的理解与支持，祝您用餐愉快！</strong></p>\",\"rules\":[{\"businessTypeId\":10,\"subBusinessTypeId\":8,\"businessTypeCode\":\"waimai\",\"subBusinessTypeCode\":\"waimai\",\"businessTypeName\":\"外卖\",\"subBusinessTypeName\":\"外卖订单问题\",\"robotId\":4,\"robotName\":\"外卖客服自研机器人\",\"enablePath\":\"online,ivr,chat,selfHelp\",\"robotTypeId\":0,\"robotTypeName\":\"业务机器人\",\"id\":2097,\"name\":\"【峰值公告】峰值等级-繁忙\",\"serviceRobot\":true}],\"applySceneId\":6,\"applySceneName\":\"公告策略\"},\"messageIdList\":[\"N4284_1746789064936\"]}]}],\"messageItemMapping\":{\"N4284_1746788912807\":\"21f211bc-0c8b-45a8-892f-adb0fada72a4\",\"1920798092677525587\":\"07c6b7f8-c701-4dcd-8dd1-bd7d7cb8ba98\",\"N4284_1746788926254\":\"6301e9da-c806-4772-bd34-7c676d73a386\",\"1920798139100459061\":\"7a316eb8-9813-434b-81ce-49145254f0e0\",\"1920798156418740228\":\"7a316eb8-9813-434b-81ce-49145254f0e0\",\"N4284_1746789064936\":\"484fc768-4f1e-44cf-a34e-908886244c40\"},\"sessionCustomTagList\":[{\"tagName\":\"task超时退出\",\"tagCode\":\"taskTimeoutExit\",\"color\":\"red\"}],\"taskExceptItemList\":[],\"taskTimeoutItemList\":[]},\"msg\":\"成功\",\"code\":\"200\",\"success\":true}";

        result = Lion.getString(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_FUYAO_SERVICE_PROCESS_CONTENT, result);
        return result;
    }

    /**
     * es数据不足的过渡方法
     * 检查查询条件是否仅支持数据库查询
     *
     * @param param
     * @return
     */
    private boolean checkConditionOnlySupportDb(PageParam<InspectWorkbenchConditionParam> param) {
        InspectWorkbenchConditionParam conditionParam = param.getCondition();
        if (conditionParam.getIsExceptionEnd() != null || conditionParam.getIsTimeoutEnd() != null) {
            return false;
        }

        if (StringUtils.isNotEmpty(conditionParam.getUserId()) || StringUtils.isNotEmpty(conditionParam.getOrderId())
                || StringUtils.isNotEmpty(conditionParam.getQuestionName())
                || StringUtils.isNotEmpty(conditionParam.getApplicationVersionId())) {
            return false;
        }
        if (StringUtils.isNotEmpty(conditionParam.getVisitId()) || StringUtils.isNotEmpty(conditionParam.getBu())
                || StringUtils.isNotEmpty(conditionParam.getSubBu()) || StringUtils.isNotEmpty(conditionParam.getStars())
                || StringUtils.isNotEmpty(conditionParam.getScene()) || StringUtils.isNotEmpty(conditionParam.getSessionSolved())) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(conditionParam.getTransferStaff())) {
            return false;
        }

        if (StringUtils.isNotEmpty(conditionParam.getTaskKey()) || StringUtils.isNotEmpty(conditionParam.getTaskVersion())
                || StringUtils.isNotEmpty(conditionParam.getTaskNode())) {
            return false;
        }

        return true;
    }

    @Override
    public PageData<InspectWorkbenchDTO> pageSessionNew(PageParam<InspectWorkbenchConditionParam> condition) {
        PageData<InspectWorkbenchDTO> pageData;

        String channel = param.getCondition().getChannel();
        boolean shouldQueryES = !checkConditionOnlySupportDb(param) || !gotoDb();
        if (StringUtils.isNotEmpty(channel) && ChannelEnum.ONLINE.getCode().equals(channel)) {
            // 若存在自定义筛选项，走es ；若不存在自定义筛选项，且lion关闭走db，则继续走es
            if (shouldQueryES) {
                return convertTo(searchOlineSessionFromES(param));
            }
        }

        if (StringUtils.isNotBlank(param.getCondition().getSessionId())) {
            List<String> sessionIdList = splitString(param.getCondition().getSessionId());
            if (sessionIdList.size() > 1) {
                pageData = listSessionByIds(param, sessionIdList);
                tryFillFieldInfoFromEs(shouldQueryES, channel, param, pageData);
                if (StringUtils.isNotEmpty(channel) && ChannelEnum.IVR.getCode().equals(channel)) {
                    appendEvalStatus(pageData);
                }
                return pageData;
            }
        }

        // 查询条件包含质检状态时，走评测的数据库，提高查询速度
        if (param.getCondition().getInspectionStatus() != null) {
            // 走评测质检表，2025-05-16只有电话渠道会走，但实际没数据。等前端下掉这里再删除
            pageData = pageSessionWithInspectionStatusWithPbAidaTraffic(param);
        } else if (StringUtils.isNotBlank(param.getCondition().getSessionId())) {
            // 查询条件包含会话ID时，结果一定只有一条，此时可以优化查询方案，提高查询速度
            pageData = pageSessionWithSessionId(param);
        } else {
            // TODO: 2025/2/14 临时方案，长期方案依赖AI搭接入ES
            // 查询条件不包含会话ID时，只能走列表查询
            // 走aida查询message，暂不含渠道信息
            pageData = pageSessionWithNoSessionId(param);
        }
        tryFillFieldInfoFromEs(shouldQueryES, channel, param, pageData);
        // 补充点赞点踩和质检信息
        if (StringUtils.isNotEmpty(channel) && ChannelEnum.IVR.getCode().equals(channel)) {
            appendEvalStatus(pageData);
        }
        return pageData;
    }

    @Override
    public PageData<InspectWorkbenchDTO> pageSession(PageParam<InspectWorkbenchConditionParam> param) {
        PageData<InspectWorkbenchDTO> pageData;

        String channel = param.getCondition().getChannel();
        boolean shouldQueryES = !checkConditionOnlySupportDb(param) || !gotoDb();
        if (StringUtils.isNotEmpty(channel) && ChannelEnum.ONLINE.getCode().equals(channel)) {
            // 若存在自定义筛选项，走es ；若不存在自定义筛选项，且lion关闭走db，则继续走es
            if (shouldQueryES) {
                return convertTo(searchOlineSessionFromES(param));
            }
        }

        if (StringUtils.isNotBlank(param.getCondition().getSessionId())) {
            List<String> sessionIdList = splitString(param.getCondition().getSessionId());
            if (sessionIdList.size() > 1) {
                pageData = listSessionByIds(param, sessionIdList);
                tryFillFieldInfoFromEs(shouldQueryES, channel, param, pageData);
                if (StringUtils.isNotEmpty(channel) && ChannelEnum.IVR.getCode().equals(channel)) {
                    appendEvalStatus(pageData);
                }
                return pageData;
            }
        }

        // 查询条件包含质检状态时，走评测的数据库，提高查询速度
        if (param.getCondition().getInspectionStatus() != null) {
            // 走评测质检表，2025-05-16只有电话渠道会走，但实际没数据。等前端下掉这里再删除
            pageData = pageSessionWithInspectionStatusWithPbAidaTraffic(param);
        } else if (StringUtils.isNotBlank(param.getCondition().getSessionId())) {
            // 查询条件包含会话ID时，结果一定只有一条，此时可以优化查询方案，提高查询速度
            pageData = pageSessionWithSessionId(param);
        } else {
            // TODO: 2025/2/14 临时方案，长期方案依赖AI搭接入ES
            // 查询条件不包含会话ID时，只能走列表查询
            // 走aida查询message，暂不含渠道信息
            pageData = pageSessionWithNoSessionId(param);
        }
        tryFillFieldInfoFromEs(shouldQueryES, channel, param, pageData);
        // 补充点赞点踩和质检信息
        if (StringUtils.isNotEmpty(channel) && ChannelEnum.IVR.getCode().equals(channel)) {
            appendEvalStatus(pageData);
        }
        return pageData;
    }

    private void tryFillFieldInfoFromEs(boolean shouldQueryES, String channel, PageParam<InspectWorkbenchConditionParam> param, PageData<InspectWorkbenchDTO> pageData) {
        // 从es补数据 复用开关
        Boolean queryMessageCount = Lion.getBoolean(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_QUERY_MESSAGE_COUNT, true);
        if (queryMessageCount != null && queryMessageCount && !shouldQueryES
                && StringUtils.isNotEmpty(channel) && ChannelEnum.ONLINE.getCode().equals(channel)) {

            try {
                PageData<AidaOnlineSessionEsDTO> esPageData = searchOlineSessionFromES(param);
                List<AidaOnlineSessionEsDTO> esData = esPageData.getData();
                Map<Long, String> aidaSceneIdNameMap = findSceneIdNameMap();
                if (CollectionUtils.isNotEmpty(esData)) {
                    // 这里只补会话维度关键字段
                    Map<String, AidaOnlineSessionEsDTO> sessionId2EsDtoMap = Maps.newConcurrentMap();
                    for (AidaOnlineSessionEsDTO esDTO : esData) {
                        sessionId2EsDtoMap.put(esDTO.getSessionId(), esDTO);
                    }

                    for (InspectWorkbenchDTO workbenchDTO : pageData.getData()) {
                        AidaOnlineSessionEsDTO esDTO = sessionId2EsDtoMap.get(workbenchDTO.getSessionId());
                        if (esDTO == null) {
                            continue;
                        }
                        fillSessionField(workbenchDTO, esDTO, aidaSceneIdNameMap);
                    }
                }
            } catch (Exception e) {
                log.error("pageSession, 补数据失败, param: {}, error: {}", JSON.toJSONString(param), e.getMessage(), e);
            }

        }
    }

    private PageData<InspectWorkbenchDTO> listSessionByIds(PageParam<InspectWorkbenchConditionParam> param, List<String> sessionIdList) {
        InspectWorkbenchConditionParam condition = param.getCondition();
        if (StringUtils.isNotBlank(condition.getApplicationId())) {
            condition.setApplicationIdList(Collections.singletonList(condition.getApplicationId()));
        } else {
            throw new CheckException("批量查询需要指定Aida应用");
        }
        // 查询会话列表
        Set<String> sessionIds = new HashSet<>(sessionIdList).stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 根据inspectionStatus条件是否为null, 选择从质检表里过滤出sessionId（应用信息、时间信息统一使用aidaMessages过滤）
        sessionIds = filterSessionIdBySessionIdsAndInspectStatus(sessionIds, condition.getInspectionStatus());

        if (CollectionUtils.isEmpty(sessionIds)) {
            return PageData.create(0, param.getPageNum(), param.getPageSize(), Collections.emptyList());
        }

        // 根据质检表过滤出的sessionId列表、再根据startTime、endTime、applicationId过滤出aidaMessages
        // 构造分页
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<AidaMessagesSessionGroupResultParam> result = aidaMessagesGeneratorService.listSessionGroupBySessionIdsAndCondition(new AidaMessagesConditionParam(condition), sessionIds);
        PageInfo<AidaMessagesSessionGroupResultParam> pageInfo = new PageInfo<>(result);
        PageHelper.clearPage();

        return operationAnalysisHelper.listInspectWorkbenchBySessionIds(pageInfo);
    }

    @Override
    public List<InspectWorkbenchSessionDetailDTO> sessionDetail(String sessionId, Integer platformType, List<String> llmSessionIdList) {
        List<InspectWorkbenchSessionDetailDTO> sessionDetailDTOList = getSessionInfoFromCache(sessionId);
        if (CollectionUtils.isEmpty(sessionDetailDTOList)) {
            // 检查sessionId是否可以处理
            SessionBizUtil.sessionCheck(sessionId);
            sessionDetailDTOList = getInspectWorkbenchSessionDetailList(sessionId, platformType, llmSessionIdList);
            // 对sessionDetailDTOList中每条isLlm的消息进行aidaMessages匹配
            if (Objects.equals(platformType, PlatformTypeEnum.AI.getCode())) {
                operationAnalysisHelper.setMessageLevelAidaAppInfo(sessionDetailDTOList, sessionId);
            }

            try {
                buildBaseInfoList(sessionId, sessionDetailDTOList);
            } catch (Exception e) {
                log.error("InspectWorkbench 填充熔断、执行动作 sessionId:{},platformType:{},llmSessionIdList:{}", sessionId, platformType, JSON.toJSONString(llmSessionIdList), e);
            } finally {
                saveSessionInfo2Cache(sessionId, sessionDetailDTOList);
            }
        }

        // 填充点赞点踩信息
        try {
            List<InspectWorkbenchEvalPo> inspectWorkbenchEvalPoList = inspectWorkbenchEvalGeneratorService.getBySessionId(sessionId);
            if (CollectionUtils.isEmpty(inspectWorkbenchEvalPoList)) {
                return sessionDetailDTOList;
            }
            Map<String, InspectWorkbenchEvalPo> inspectMap = inspectWorkbenchEvalPoList.stream().collect(Collectors.toMap(InspectWorkbenchEvalPo::getMessageId, Function.identity(), (a, b) -> a.getEvalTime().after(b.getEvalTime()) ? a : b));

            sessionDetailDTOList.forEach(dto -> {
                dto.setManualInspect(buildManualInspect(platformType, dto, inspectMap));
            });
        } catch (Exception e) {
            log.error("InspectWorkbench 填充点赞点踩信息失败 sessionId:{},platformType:{},llmSessionIdList:{}", sessionId, platformType, JSON.toJSONString(llmSessionIdList), e);
        }

        return sessionDetailDTOList;
    }

    private void buildBaseInfoList(String sessionId, List<InspectWorkbenchSessionDetailDTO> sessionDetailDTOList) {
        long currentTimeMillis = System.currentTimeMillis();
        //  过滤出sessionDetailDTOList中所有isLlm为true的消息的llmMessageId列表
        Map<String, String> messageIdApplicationIdMap = CollectionUtils.emptyIfNull(sessionDetailDTOList).stream()
                .filter(dto -> Objects.equals(dto.getIsLlm(), Boolean.TRUE))
                .map(InspectWorkbenchSessionDetailDTO::getMessageLevelAidaAppInfo)
                .filter(Objects::nonNull)
                .filter(messageLevelAidaAppInfoDTO -> StringUtils.isNotBlank(messageLevelAidaAppInfoDTO.getLlmMessageId()))
                .filter(messageLevelAidaAppInfoDTO -> StringUtils.isNotBlank(messageLevelAidaAppInfoDTO.getApplicationId()))
                .collect(Collectors.toMap(MessageLevelAidaAppInfoDTO::getLlmMessageId, MessageLevelAidaAppInfoDTO::getApplicationId, (a, b) -> a));

        List<String> llmMessageIdList = new ArrayList<>(messageIdApplicationIdMap.keySet());
        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> aidaTraceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(llmMessageIdList)) {
            AidaMessagesConditionParam condition = new AidaMessagesConditionParam();
            condition.setSessionId(sessionId);
            List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByCondition(condition);
            //  查询AidaTraceMap，根据llmMessageIdList查询对应的AidaTrace信息
            aidaTraceMap = getAidaTraceMap(aidaMessagesPoList, llmMessageIdList);
        }

        // 填充熔断信息
        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo>> fuseMap = aidaTraceMap.entrySet().stream()
                .map(pair -> Pair.of(pair.getKey(), convertFuse(pair.getValue(), messageIdApplicationIdMap.get(pair.getKey()))))
                .filter(pair -> Objects.nonNull(pair.getValue()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (a, b) -> a));

        // 填充动作信息
        Map<String, InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> actionMap = aidaTraceMap.entrySet().stream()
                .map(pair -> Pair.of(pair.getKey(), convertAction(pair.getValue())))
                .filter(pair -> Objects.nonNull(pair.getValue()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (a, b) -> a));

        log.info("InspectWorkbench session/detail 获取AidaTrace  sessionId:{},对话次数={},耗时={}毫秒", sessionId, llmMessageIdList.size(), (System.currentTimeMillis() - currentTimeMillis));

        // 填充熔断、执行动作
        if (MapUtils.isNotEmpty(fuseMap) || MapUtils.isNotEmpty(actionMap)) {
            sessionDetailDTOList.forEach(dto -> {
                // 填充基础信息
                convertBaseInfoList(dto, fuseMap, actionMap);
            });
        }
    }

    private InspectWorkbenchSessionDetailDTO.ManualInspect buildManualInspect(Integer platformType, InspectWorkbenchSessionDetailDTO dto, Map<String, InspectWorkbenchEvalPo> inspectMap) {
        // 若是aida平台, 且判断是可以进行query detail的消息, 才填充赞踩
        boolean canSetInspectInfo;
        if (Objects.equals(platformType, PlatformTypeEnum.AI.getCode())) {
            canSetInspectInfo = operationAnalysisHelper.judgeInspectWorkbenchSessionDetailCanQueryDetail(dto);
        } else {
            canSetInspectInfo = ((null != dto) && (Objects.equals(dto.getIsLlm(), Boolean.TRUE)));
        }

        if (canSetInspectInfo && inspectMap.containsKey(dto.getMessageId())) {
            InspectWorkbenchSessionDetailDTO.ManualInspect manualInspect = new InspectWorkbenchSessionDetailDTO.ManualInspect();
            manualInspect.setInspectMis(inspectMap.get(dto.getMessageId()).getEvalMis());
            manualInspect.setInspectResult(inspectMap.get(dto.getMessageId()).getAgreeStatus());
            return manualInspect;
        }
        return null;
    }

    /**
     * 保存会话详情缓存
     *
     * @param sessionId            会话ID
     * @param sessionDetailDTOList 会话详情列表
     */
    private void saveSessionInfo2Cache(String sessionId, List<InspectWorkbenchSessionDetailDTO> sessionDetailDTOList) {
        if (CollectionUtils.isEmpty(sessionDetailDTOList)) {
            return;
        }
        try {
            String sessionResultStr = JSONObject.toJSONString(sessionDetailDTOList);
            int maxCacheLength = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.CACHE_MAX_LENGTH, 1656061);
            if (sessionResultStr.length() > maxCacheLength) {
                return;
            }
            // 过期时间
            int expireTime = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_CACHE_EXPIRE_TIME, 60 * 30);
            redisClientProxy.set(new StoreKey(RedisConstants.WORKBENCH_SESSION_LIST_CACHE, sessionId), sessionResultStr, expireTime);
        } catch (Exception e) {
            log.error("saveSessionInfo2Cache error,sessionId={}", sessionId, e);
        }
    }

    /**
     * 获取会话详情缓存
     *
     * @param sessionId 会话ID
     * @return 会话详情列表
     */
    private List<InspectWorkbenchSessionDetailDTO> getSessionInfoFromCache(String sessionId) {
        try {
            String sessionDetailDTOListStr = redisClientProxy.get(new StoreKey(RedisConstants.WORKBENCH_SESSION_LIST_CACHE, sessionId));
            if (StringUtils.isNotBlank(sessionDetailDTOListStr)) {
                return JSONObject.parseArray(sessionDetailDTOListStr, InspectWorkbenchSessionDetailDTO.class);
            }
        } catch (Exception e) {
            log.error("getSessionInfoFromCache error,sessionId={}", sessionId, e);
        }
        return null;
    }

    /**
     * 获取会话详情
     *
     * @param sessionId        会话ID
     * @param platformType     平台类型
     * @param llmSessionIdList 大模型会话ID列表
     * @return 会话详情列表
     */
    private List<InspectWorkbenchSessionDetailDTO> getInspectWorkbenchSessionDetailList(String sessionId, Integer platformType, List<String> llmSessionIdList) {
        CommonUtils.checkEval(platformType != null, "平台类型不能为空");
        UserInfo userInfo;
        if (platformType == PlatformTypeEnum.AI.getCode()) {
            userInfo = getAidaUserInfo(llmSessionIdList);
        } else {
            userInfo = getPbUserInfo(sessionId);
        }
        return getSessionHistoryDetails(sessionId, userInfo);
    }

    /**
     * 获取过滤后的会话历史详情
     *
     * @param sessionId 会话ID
     * @param userInfo  用户信息 {@link UserInfo}
     * @return 过滤后的会话历史详情列表
     */
    private List<InspectWorkbenchSessionDetailDTO> getFilteredSessionHistoryDetails(String sessionId, UserInfo userInfo) {
        List<MessageDTO> messageList = smartInvokeServiceProxy.getHistoryListBySessionIdDesc(Long.valueOf(sessionId.trim()), userInfo.getUserType(), userInfo.getUserId());
        if (CollectionUtils.isEmpty(messageList)) {
            log.error("未拉取到在线侧数据, sessionId:{}, UserInfo:{}", sessionId, JSONObject.toJSONString(userInfo));
            throw new CheckException("未拉取到在线侧数据");
        }
        List<String> filerMessageType = Lion.getList(ConfigUtil.getAppkey(), LionConstants.FILTER_MESSAGE_TYPE, String.class);
        return messageList.stream().filter(message -> !ChatMessageTypeEnum.FILTER_MESSAGE_TYPE.contains(message.getType()) && (CollectionUtils.isEmpty(filerMessageType) || !filerMessageType.contains(message.getType()))).map(messageDTO -> convertSession(messageDTO)).collect(Collectors.toList());
    }

    /**
     * 从电话侧获取对话详情
     *
     * @param sessionId
     * @return
     */
    private List<InspectWorkbenchSessionDetailDTO> getIvrSessionHistoryDetails(String sessionId) {
        List<InspectWorkbenchSessionDetailDTO> inspectWorkbenchSessionDetailDTOList = Lists.newArrayList();
        try {
            String sessionCache = redisClientProxy.get(StoreKey.valueOf(RedisConstants.WORKBENCH_SESSION_CACHE, sessionId));
            if (StringUtils.isNotBlank(sessionCache)) {
                return JSONArray.parseArray(sessionCache, InspectWorkbenchSessionDetailDTO.class);
            }

            IvrSessionInfoDTO ivrSessionInfoDTO = ivrInvokeServiceProxy.getHistoryListBySessionId(sessionId.trim());
            if (ivrSessionInfoDTO == null) {
                log.error("从电话侧获取对话详情失败, sessionId:{}", sessionId);
                throw new CheckException("从电话侧获取对话详情失败");
            }
            inspectWorkbenchSessionDetailDTOList = convertIvrSession(ivrSessionInfoDTO);

            redisClientProxy.set(StoreKey.valueOf(RedisConstants.WORKBENCH_SESSION_CACHE, sessionId), JSON.toJSONString(inspectWorkbenchSessionDetailDTOList));
        } catch (Exception e) {
            log.error("从电话侧获取对话详情异常, applicationId:{}, sessionId:{}", "applicationId", sessionId, e);
            throw new CheckException("从电话侧获取对话详情异常");
        }

        return inspectWorkbenchSessionDetailDTOList;
    }

    @Override
    public void saveLog(String sessionId) {
        String mis = UserUtils.getUser().getLogin();
        WORKBENCH_INSPECTION_LOG_THREAD_POOL.submit(() -> saveSessionLog(sessionId, mis));
    }

    @Override
    public InspectWorkbenchDTO sessionOverview(String sessionId) {
        // 检查sessionId
        SessionBizUtil.sessionCheck(sessionId);
        PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        InspectWorkbenchConditionParam condition = new InspectWorkbenchConditionParam();
        condition.setSessionId(sessionId);

        pageParam.setCondition(condition);
        PageData<InspectWorkbenchDTO> pageResult = pageSessionWithSessionId(pageParam);

        if (CollectionUtils.isNotEmpty(pageResult.getData())) {
            InspectWorkbenchDTO inspectWorkbenchDTO = pageResult.getData().get(0);
            if (null == inspectWorkbenchDTO || (Objects.equals(inspectWorkbenchDTO.getPlatformType(), ThirdPlatformTypeEnum.AIDA.getCode()) && CollectionUtils.isEmpty(inspectWorkbenchDTO.getWorkspaceAppInfo()))) {
                log.warn(String.format("获取/session/overview无空间应用数据:sessionId:%s", sessionId));
                return new InspectWorkbenchDTO();
            }
            return inspectWorkbenchDTO;
        }
        log.warn(String.format("获取/session/overview没有数据, sessionId:%s", sessionId));
        return new InspectWorkbenchDTO();
    }

    @Override
    public InspectWorkbenchDTO sessionBasic(String sessionId, String analysisType) {
        InspectWorkbenchDTO inspectWorkbenchDTO = sessionOverview(sessionId);

        try {
            fillSessionFieldFromEs(inspectWorkbenchDTO, analysisType);
            if (ChannelEnum.ONLINE.getCode().equals(analysisType)) {
                appendServiceProcessInfo(inspectWorkbenchDTO.getSessionId(), inspectWorkbenchDTO.getTime().getTime(), inspectWorkbenchDTO);
            }
            ReferenceDebuggingWindowWidthSetUpDTO windowWidthSetUp = getWindowWidthSetUp();
            inspectWorkbenchDTO.setWindowWidthSetUp(windowWidthSetUp);
        } catch (Exception e) {
            log.error("sessionBasic error, sessionId={}, analysisType={}", sessionId, analysisType, e);
        }
        return inspectWorkbenchDTO;
    }

    /**
     * 从es中获取会话信息并填充到inspectWorkbenchDTO中
     *
     * @param inspectWorkbenchDTO
     * @param channel
     */
    private void fillSessionFieldFromEs(InspectWorkbenchDTO inspectWorkbenchDTO, String channel) {
        if (ChannelEnum.ONLINE.getCode().equals(channel)) {
            String sessionId = inspectWorkbenchDTO.getSessionId();
            OnlineSessionSearchDTO searchDTO = new OnlineSessionSearchDTO();
            searchDTO.setSessionIdList(Arrays.asList(sessionId));
            PageData<AidaOnlineSessionEsDTO> aidaOnlineSessionEsDTOPageData = onlineSessionSearchService.pageSessions(searchDTO);

            if (CollectionUtils.isNotEmpty(aidaOnlineSessionEsDTOPageData.getData())) {
                AidaOnlineSessionEsDTO esDTO = aidaOnlineSessionEsDTOPageData.getData().get(0);
                if (esDTO != null) {
                    Set<String> workspaceIdSet = Sets.newHashSet();
                    Set<String> applicationIdSet = Sets.newHashSet();
                    Set<String> versionIdSet = Sets.newHashSet();

                    if (CollectionUtils.isNotEmpty(esDTO.getAidaAppInfo())) {
                        for (AidaOnlineSessionEsDTO.AidaAppInfo aidaAppInfo : esDTO.getAidaAppInfo()) {
                            workspaceIdSet.add(aidaAppInfo.getSpaceId());
                            applicationIdSet.add(aidaAppInfo.getAppId());
                            versionIdSet.add(aidaAppInfo.getVersionId());
                        }
                    }

                    List<String> workspaceIdList = new ArrayList<>(workspaceIdSet);
                    List<String> applicationIdList = new ArrayList<>(applicationIdSet);
                    List<String> versionIdList = new ArrayList<>(versionIdSet);

                    // 根据appId列表查询出app信息
                    List<AppDTO> apps = aidaInvokeServiceProxy.listAidaAppDtoByIds(applicationIdList);
                    // 根据workspaceId列表查询出workspace信息
                    List<TenantDTO> tenantDTOS = aidaInvokeServiceProxy.listAidaTenantDtoByIds(workspaceIdList);
                    Map<String, String> workspaceNameMap = tenantDTOS.stream().collect(Collectors.toMap(TenantDTO::getId, TenantDTO::getName, (a, b) -> a));
                    // 更具应用信息构建应用id和应用结构的映射表
                    Map<String, AppDTO> appMap = apps.stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));
                    Map<String, String> appVersionIdNameMap = Maps.newConcurrentMap();
                    // todo 批量接口
                    for (String versionId : versionIdList) {
                        VersionConfigDTO versionConfigDTO = aidaInvokeServiceProxy.getVersionConfigByVersionId(versionId);
                        if (versionConfigDTO != null) {
                            appVersionIdNameMap.put(versionId, versionConfigDTO.getVersionName());
                        }
                    }

                    Map<Long, String> aidaSceneIdNameMap = findSceneIdNameMap();
                    fillAllFieldInfo(inspectWorkbenchDTO, esDTO, aidaSceneIdNameMap, appMap, workspaceNameMap, appVersionIdNameMap);
                }
            }

        }
    }

    /**
     * 获取场景ID和名称的映射关系
     *
     * @return
     */
    private Map<Long, String> findSceneIdNameMap() {
        Map<Long, String> aidaSceneIdNameMap = Maps.newHashMap();
        AidaResponse<List<AidaSceneDTO>> allScenesResponse = aidaSceneRemoteService.listAllScenes();
        if (allScenesResponse.getCode() == 0 && CollectionUtils.isNotEmpty(allScenesResponse.getData())) {
            for (AidaSceneDTO aidaSceneDTO : allScenesResponse.getData()) {
                aidaSceneIdNameMap.put(aidaSceneDTO.getId(), aidaSceneDTO.getSceneName());
            }
        }
        return aidaSceneIdNameMap;
    }

    /**
     * 获取参考调试窗口宽度设置
     *
     * @return
     */
    private ReferenceDebuggingWindowWidthSetUpDTO getWindowWidthSetUp() {
        String userMis = Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse("");
        try {
            String type = CustomConfigTypeEnum.REFERENCE_DEBUGGING_WINDOW_WIDTH.getCode();
            WorkbenchCustomConfigCondition customConfigCondition = new WorkbenchCustomConfigCondition();
            customConfigCondition.setCreatorMis(userMis);
            customConfigCondition.setType(type);
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<WorkbenchCustomConfigPo> workbenchCustomConfigList = workbenchCustomConfigGeneratorService.listByCondition(customConfigCondition);
            ZebraForceMasterHelper.clearLocalContext();
            WorkbenchCustomConfigPo workbenchCustomConfigPo = null;
            if (CollectionUtils.isNotEmpty(workbenchCustomConfigList)) {
                workbenchCustomConfigPo = workbenchCustomConfigList.get(0);
            }
            if (Objects.isNull(workbenchCustomConfigPo)) {
                return null;
            }
            String config = workbenchCustomConfigPo.getConfig();
            if (StringUtils.isNotBlank(config)) {
                return JSON.parseObject(config, new TypeReference<ReferenceDebuggingWindowWidthSetUpDTO>() {
                });
            }
        } catch (Exception e) {
            log.error("用户: {}获取参考调试窗口宽度设置异常：", userMis, e);
        }
        return null;
    }

    @Override
    public String sessionIvrInfo(String sessionId) {
        return ivrInvokeServiceProxy.getOriginDataBySessionId(sessionId);
    }

    @Override
    public InspectWorkbenchQueryDetailDTO queryDetail(InspectWorkbenchDetailParam param) {
        try {
            CommonUtils.checkEval(param.getPlatform() != null, "平台类型不能为空");
            if (PlatformTypeEnum.AI.getCode() == param.getPlatform()) {
                return getAidaMessageDetailFromEval(param);
            }
            return getPbDetail(param);
        } catch (Exception e) {
            Cat.logError(new LlmMessageNotFoundException(e));
            throw e;
        }
    }

    @Override
    public InspectWorkbenchQueryDetailPathDTO queryDetailPath(InspectWorkbenchDetailPathParam param) {
        CommonUtils.checkEval(param.getPlatform() == PlatformTypeEnum.AI.getCode(), "不支持非AI搭平台查询执行链路信息");
        return getAidaAppExecutionPathFromEval(param);
    }

    @Override
    public void saveLog(InspectWorkbenchDetailParam param) {
        String mis = UserUtils.getUser().getLogin();
        WORKBENCH_INSPECTION_LOG_THREAD_POOL.submit(() -> saveQueryLog(param.getSessionId(), param.getLlmMessageId(), mis));
    }

    @Override
    public void queryInspect(InspectWorkbenchAgreeParam param) {
        String userMis = UserUtils.getUser().getLogin();
        List<InspectWorkbenchEvalPo> evalPoList = inspectWorkbenchEvalGeneratorService.getBySessionIdAndMessageId(param.getSessionId(), param.getMessageId());
        if (CollectionUtils.isNotEmpty(evalPoList)) {
            for (InspectWorkbenchEvalPo evalPo : evalPoList) {
                if (Objects.equals(evalPo.getEvalMis(), userMis)) {
                    if (evalPo.getAgreeStatus().equals(param.getIsAgree())) {
                        throw new CheckException("请不要重复操作！");
                    } else {
                        evalPo.setAgreeStatus(param.getIsAgree());
                        inspectWorkbenchEvalGeneratorService.updateById(evalPo);
                        return;
                    }

                }

            }
        }
        InspectWorkbenchEvalPo inspectWorkbenchEvalPo = new InspectWorkbenchEvalPo();
        inspectWorkbenchEvalPo.setSessionId(param.getSessionId());
        inspectWorkbenchEvalPo.setLlmSessionId(String.join(",", param.getLlmSessionId()));
        inspectWorkbenchEvalPo.setMessageId(param.getMessageId());
        inspectWorkbenchEvalPo.setLlmMessageId(param.getLlmMessageId());
        inspectWorkbenchEvalPo.setEvalTime(new Date());
        inspectWorkbenchEvalPo.setEvalMis(userMis);
        inspectWorkbenchEvalPo.setPlatformType(param.getPlatformType());
        inspectWorkbenchEvalPo.setAgreeStatus(param.getIsAgree());
        inspectWorkbenchEvalGeneratorService.save(inspectWorkbenchEvalPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void queryCollect(InspectWorkbenchCollectParam param) {
        // AI搭暂不支持
        CommonUtils.checkEval(PlatformTypeEnum.AI.getCode() != param.getPlatformType(), "AI搭暂不支持加入错题集");
        saveWorkbenchMistake(param);
    }

    @Override
    public String getSceneTypeNameBySessionId(String sessionId) {
        SceneInfoDTO sceneInfoDTO = pbServiceProxy.getSceneInfoBySessionId(sessionId);
        if (sceneInfoDTO == null) {
            return null;
        }
        // 获取场景与工作空间映射关系
        List<SceneRelation> list = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SCENE_WORKSPACE_RELATION, SceneRelation.class);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, SceneRelation> sceneRelationMap = list.stream().collect(Collectors.toMap(SceneRelation::getSceneTypeName, Function.identity()));
            for (String sceneTypeName : sceneRelationMap.keySet()) {
                for (List<String> keywords : sceneRelationMap.get(sceneTypeName).getKeywordsList()) {
                    if (keywords.stream().allMatch(keyword -> sceneInfoDTO.getSceneName().contains(keyword))) {
                        return sceneTypeName;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取自动质检需要的会话详情信息
     *
     * @param request 请求参数
     * @return 返回会话详情信息
     */
    @Override
    public WorkbenchAutoInspectDTO getSessionDetailInfo(AutoInspectionRequestParam request) {
        List<InspectWorkbenchSessionDetailDTO> sessionDetailDtoList = sessionDetail(request.getSessionId(), request.getPlatformType(), request.getLlmSessionId());
        List<InspectWorkbenchDetailDTO> data = sessionDetailDtoList.stream()
                .filter(InspectWorkbenchSessionDetailDTO::getIsLlm)
                .map(dto -> {
                    InspectWorkbenchDetailDTO workbenchDetailDTO = new InspectWorkbenchDetailDTO();
                    BeanUtils.copyProperties(dto, workbenchDetailDTO);
                    InspectWorkbenchDetailParam inspectWorkbenchDetailParam = getInspectWorkbenchDetailParam(request, dto);
                    // todo n+1 问题
                    InspectWorkbenchQueryDetailDTO queryDetailDTO = queryDetail(inspectWorkbenchDetailParam);
                    if (request.getPlatformType() == ThirdPlatformTypeEnum.AIDA.getCode()) {
                        removeAidaUnusedData(queryDetailDTO);
                    }
                    workbenchDetailDTO.setQueryDetail(queryDetailDTO);
                    return workbenchDetailDTO;
                }).collect(Collectors.toList());

        WorkbenchAutoInspectDTO autoInspectDTO = new WorkbenchAutoInspectDTO();
        autoInspectDTO.setPlatformType(request.getPlatformType());
        autoInspectDTO.setData(data);
        autoInspectDTO.setSessionId(request.getSessionId());
        return autoInspectDTO;
    }

    @Override
    public List<WorkbenchAutoInspectDTO> getSessionDetailInfoBatch(List<String> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Lists.newArrayList();
        }
        return sessionIds.stream().map(sessionId -> {
            try {
                return new WorkbenchAutoInspectDTO();
            } catch (Exception e) {
                log.info("导出自动质检数据失败, sessionId: {}", sessionId, e);
                WorkbenchAutoInspectDTO autoInspectDTO = new WorkbenchAutoInspectDTO();
                autoInspectDTO.setSessionId(sessionId);
                return autoInspectDTO;
            }
        }).collect(Collectors.toList());
    }

    @Override
    public List<WorkspaceAppDTO> getWorkspacesAndApps() {
        List<WorkspaceAppDTO> workspaceAppList = getWorkspaceAppInfo();
        return workspaceAppList;
    }

    /**
     * 将场景下app转换成现有的。 暂未统一
     *
     * @param aidaSceneAppHierarchyDTOList
     * @return
     */
    private List<WorkspaceAppDTO> convertTo(List<AidaSceneAppHierarchyDTO> aidaSceneAppHierarchyDTOList) {
        List<WorkspaceAppDTO> workspaceAppDTOList = Lists.newArrayList();
        fillAidaInfo(aidaSceneAppHierarchyDTOList);

        for (AidaSceneAppHierarchyDTO sceneApplicationDTO : aidaSceneAppHierarchyDTOList) {
            List<SpaceDTO> spaces = sceneApplicationDTO.getSpaces();
            if (CollectionUtils.isNotEmpty(spaces)) {
                for (SpaceDTO spaceDTO : spaces) {
                    WorkspaceAppDTO workspaceAppDTO = new WorkspaceAppDTO();
                    workspaceAppDTO.setWorkspaceId(spaceDTO.getSpaceId());
                    workspaceAppDTO.setWorkspaceName(spaceDTO.getSpaceName());
                    workspaceAppDTOList.add(workspaceAppDTO);


                    List<com.meituan.csc.aigc.runtime.dto.aida.AppDTO> appDTOS = spaceDTO.getApps();
                    if (CollectionUtils.isEmpty(appDTOS)) {
                        continue;
                    }

                    List<WorkspaceAppDTO.Application> applicationList = Lists.newArrayList();
                    for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : appDTOS) {
                        WorkspaceAppDTO.Application application = new WorkspaceAppDTO.Application();
                        application.setApplicationId(appDTO.getAppId());
                        application.setApplicationName(appDTO.getAppName());
                        applicationList.add(application);
                    }
                    workspaceAppDTO.setApplicationList(applicationList);
                }

            }
        }
        return workspaceAppDTOList;
    }

    /**
     * @param aidaSceneAppHierarchyDTOList
     * @return
     */
    private List<AidaRobotDTO> convertToAidaRobot(List<AidaSceneAppHierarchyDTO> aidaSceneAppHierarchyDTOList) {
        List<AidaRobotDTO> workspaceAppDTOList = Lists.newArrayList();

        for (AidaSceneAppHierarchyDTO sceneApplicationDTO : aidaSceneAppHierarchyDTOList) {
            List<SpaceDTO> spaces = sceneApplicationDTO.getSpaces();
            if (CollectionUtils.isNotEmpty(spaces)) {
                for (SpaceDTO spaceDTO : spaces) {
                    AidaRobotDTO workspaceAppDTO = new AidaRobotDTO();
                    workspaceAppDTO.setWorkspaceId(spaceDTO.getSpaceId());
                    workspaceAppDTO.setWorkspaceName(spaceDTO.getSpaceName());
                    workspaceAppDTOList.add(workspaceAppDTO);


                    List<com.meituan.csc.aigc.runtime.dto.aida.AppDTO> appDTOS = spaceDTO.getApps();
                    if (CollectionUtils.isEmpty(appDTOS)) {
                        continue;
                    }

                    List<AidaRobotDTO.Application> applicationList = Lists.newArrayList();
                    for (com.meituan.csc.aigc.runtime.dto.aida.AppDTO appDTO : appDTOS) {
                        AidaRobotDTO.Application application = new AidaRobotDTO.Application();
                        application.setApplicationId(appDTO.getAppId());
                        application.setApplicationName(appDTO.getAppName());
                        applicationList.add(application);
                    }
                    workspaceAppDTO.setApplicationList(applicationList);
                }

            }
        }
        return workspaceAppDTOList;
    }

    @Override
    public List<WorkspaceAppDTO> getWorkspacesAndAppsBySceneType(String sceneType) {
        SceneTypeEnum sceneTypeEnum = SceneTypeEnum.ONLINE;
        if (ChannelEnum.IVR.getCode().equals(sceneType)) {
            sceneTypeEnum = SceneTypeEnum.PHONE;
        }

        AidaResponse<List<AidaSceneAppHierarchyDTO>> aidaSceneResponse = aidaSceneRemoteService.listSceneApplicationsByType(sceneTypeEnum);
        log.info("getWorkspacesAndAppsBySceneType sceneTypeEnum={}, response={}", sceneTypeEnum, JSON.toJSON(aidaSceneResponse));
        if (aidaSceneResponse.getCode() == 0 && CollectionUtils.isNotEmpty(aidaSceneResponse.getData())) {
            return convertTo(aidaSceneResponse.getData());
        }

        return Collections.emptyList();
    }

    /**
     * ES方案前期少数据
     * 是否走数据库方案兜底
     *
     * @return
     */
    private boolean gotoDb() {
        return Lion.getBoolean(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SESSION_DB_SWITCH, true);
    }

    @Override
    public void exportSession(InspectWorkbenchConditionParam condition) {
        String channel = condition.getChannel();
        if (StringUtils.isBlank(condition.getApplicationId()) && StringUtils.isNotEmpty(channel)
                && ChannelEnum.IVR.getCode().equals(channel)) {
            log.warn("运营分析工具数据导出, 应用ID为空, condition: {}", JSONObject.toJSONString(condition));
            throw new EvalException("会话数据导出必须选择空间/应用");
        }
        // 设置查询条件中的应用ID列表，根据条件中的应用ID是否为空进行设置，如果为空查询所有配置上去的应用，并包装单个AppID
        condition.setApplicationIdList(Collections.singletonList(condition.getApplicationId()));

        // 查询会话ID, 前置处理，避免导出数据为空
        Set<String> sessionIds = Collections.emptySet();
        // 限制导出数量
        Integer limit = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_EXPORT_SESSION_LIMIT, 200);
        if (StringUtils.isNotEmpty(channel) && ChannelEnum.ONLINE.getCode().equals(channel)) {
            PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
            pageParam.setPageNum(1);
            pageParam.setPageSize(limit);
            pageParam.setCondition(condition);
            boolean shouldQueryES = !checkConditionOnlySupportDb(pageParam) || !gotoDb();

            if (shouldQueryES) {
                PageData<InspectWorkbenchDTO> pageData = convertTo(searchOlineSessionFromES(pageParam));
                if (CollectionUtils.isNotEmpty(pageData.getData())) {
                    List<InspectWorkbenchDTO> workbenchDTOList = pageData.getData();
                    sessionIds = workbenchDTOList.stream().map(InspectWorkbenchDTO::getSessionId).collect(Collectors.toSet());
                }
            } else if (checkConditionOnlySupportDb(pageParam)) {
                log.warn("运营分析工具数据导出, 优先走DB会话数据, pageParam: {}", JSONObject.toJSONString(pageParam));
                sessionIds = getSessionIds(condition, limit);
            }

        } else {
            sessionIds = getSessionIds(condition, limit);
        }

        // 对sessionIds筛选出能够处理的
        sessionIds = sessionIds.stream().filter(sessionId -> SessionBizUtil.judgeSessionCanProcess(sessionId, null)).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(sessionIds)) {
            log.warn("运营分析工具数据导出, 无可导出的会话数据, condition: {}", JSONObject.toJSONString(condition));
            throw new EvalException("没有可供导出的会话数据");
        }

        // 获取锁
        String mis = UserUtils.getUser().getLogin();
        StoreBoundResponse lockResponse = redisClientProxy.incrWithUpperBound(new StoreKey(RedisConstants.WORKBENCH_EXPORT_SESSION_LOCK, mis), 1, 1, 0, 1800L);
        if (!lockResponse.isOperated()) {
            log.warn("运营分析工具数据导出, 获取锁失败, mis: {}", mis);
            throw new EvalException("当前已有一个数据导出任务正在进行中，请耐心等待。");
        }
        log.info("运营分析工具数据导出, 获取锁成功, mis: {}", mis);

        // 异步导出
        Set<String> finalSessionIds = sessionIds;
        WORKBENCH_SESSION_EXPORT_POOL.submit(() -> {
            try {
                String url = sessionExportService.exportSession(mis, finalSessionIds);
                if (StringUtils.isBlank(url)) {
                    sendSessionExportFailureNotification(mis);
                } else {
                    sendSessionExportDxMessage(mis, url);
                }
            } catch (Exception e) {
                log.error("运营分析工具数据导出失败, mis: {}", mis, e);
                Cat.logError("运营分析工具数据导出失败", new EvalException(e));
                sendSessionExportFailureNotification(mis);
            } finally {
                // 释放锁
                Boolean delete = redisClientProxy.delete(new StoreKey(RedisConstants.WORKBENCH_EXPORT_SESSION_LOCK, mis));
                log.info("运营分析工具数据导出, 释放锁完成, 状态: {}, mis: {}", delete, mis);
            }
        });

        // 提交任务完成
        log.info("运营分析工具数据导出, 导出任务提交成功, mis: {}, condition: {}", mis, JSONObject.toJSONString(condition));
    }

    /**
     * 走db方案
     *
     * @param condition
     * @param limit
     * @return
     */
    private Set<String> getSessionIds(InspectWorkbenchConditionParam condition, int limit) {
        Set<String> sessionIds;
        if (StringUtils.isNotBlank(condition.getSessionId())) {
            sessionIds = new HashSet<>(splitString(condition.getSessionId()));
            // 根据inspectionStatus条件是否为null, 选择从质检表里过滤出sessionId（应用信息、时间信息统一使用aidaMessages过滤）
            sessionIds = filterSessionIdBySessionIdsAndInspectStatus(sessionIds, condition.getInspectionStatus());
            // 根据质检表过滤出的sessionId列表、再根据startTime、endTime、applicationId过滤出aidaMessages
            List<AidaMessagesSessionGroupResultParam> result = aidaMessagesGeneratorService.listSessionGroupBySessionIdsAndCondition(new AidaMessagesConditionParam(condition), sessionIds);
            sessionIds = result.stream().filter(Objects::nonNull).map(AidaMessagesSessionGroupResultParam::getSessionId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if (sessionIds.size() > limit) {
                throw new EvalException("导出会话数量超过限制，最多可导出 " + limit + " 个会话");
            }
        } else {
            limit = limit * 10;
            sessionIds = aidaMessagesGeneratorService.listSessionGroupByConditionWithGroupDate(new AidaMessagesConditionParam(condition)).stream().map(AidaMessagesSessionGroupResultParam::getSessionId).collect(Collectors.toSet());
            // 根据inspectionStatus条件是否为null, 选择从质检表里过滤出sessionId（应用信息、时间信息统一使用aidaMessages过滤）
            sessionIds = filterSessionIdBySessionIdsAndInspectStatus(sessionIds, condition.getInspectionStatus());

            // 限制会话ID数量，只取前limit个，并转换为Set集合去重
            sessionIds = sessionIds.stream()
                    .limit(limit)
                    .collect(Collectors.toSet());
        }
        return sessionIds;
    }

    private Set<String> filterByInspectStatus(InspectWorkbenchConditionParam condition, Set<String> sessionIds) {
        if (condition.getInspectionStatus() != null) {
            // 使用SessionID以外的条件查询, 内存取交集, 过滤出已质检的会话ID
            InspectWorkbenchConditionParam newCondition = new InspectWorkbenchConditionParam();
            BeanUtils.copyProperties(condition, newCondition);
            newCondition.setSessionId(null);
            List<String> list = workbenchSessionInfoGeneratorService.listSessionIdByCondition(newCondition);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptySet();
            }
            // 取SessionId交集
            sessionIds.retainAll(new HashSet<>(list));
        }
        return sessionIds;
    }

    /**
     * 根据sessionIds集合，已质检状态过滤会话ID
     *
     * @param sessionIds    会话ID集合
     * @param inspectStatus 质检状态
     * @return 过滤后的会话ID集合
     */
    private Set<String> filterSessionIdBySessionIdsAndInspectStatus(Set<String> sessionIds, Integer inspectStatus) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptySet();
        }
        if (null != inspectStatus) {
            return filterSessionIdsAndQueryDimAndTypeByWorkbenchInspectInfo(sessionIds);
        }
        return sessionIds;
    }

    /**
     * 根据sessionIds集合，查询质检详情信息，过滤出query维度，llmMessageId非null, type是人工的的会话ID
     *
     * @param sessionIds 会话ID集合
     * @return 过滤后的会话ID集合
     */
    private Set<String> filterSessionIdsAndQueryDimAndTypeByWorkbenchInspectInfo(Set<String> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Sets.newHashSet();
        }
        List<WorkbenchInspectInfoPo> list = workbenchInspectInfoGeneratorService.listBySessionIdsAndDimensionAndLLMMessageIdNotNullAndTypeAndAidaPlatform(sessionIds, DimensionTypeEnum.QUERY.getCode(), MetricEvalTypeEnum.MANUAL.getCode());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return list.stream().map(WorkbenchInspectInfoPo::getSessionId).collect(Collectors.toSet());
    }

    @Override
    public WorkbenchRoleDTO getRole() {
        return new WorkbenchRoleDTO(getCurrentUserRole());
    }


    @Override
    public void checkWorkbenchCommonParam(WorkbenchCommonParam param) {
        CommonUtils.checkEval(param != null, "参数不能为空");
        CommonUtils.checkEval(param.getDimension() != null, "质检维度不能为空");
        CommonUtils.checkEval(DimensionTypeEnum.getByCode(param.getDimension()) != null, "质检维度不合法");
        CommonUtils.checkEval(StringUtils.isNotBlank(param.getSessionId()), "会话ID不能为空");
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(param.getLlmSessionId()), "大模型会话ID不能为空");
        if (param.getDimension() == DimensionTypeEnum.QUERY.getCode()) {
            CommonUtils.checkEval(StringUtils.isNotBlank(param.getMessageId()), "消息ID不能为空");
            CommonUtils.checkEval(StringUtils.isNotBlank(param.getLlmMessageId()), "大模型消息ID不能为空");
        }
        CommonUtils.checkEval(param.getPlatformType() != null, "大模型平台类型不能为空");
        CommonUtils.checkEval(
                PlatformTypeEnum.parse(param.getPlatformType()) == PlatformTypeEnum.AI ||
                        PlatformTypeEnum.parse(param.getPlatformType()) == PlatformTypeEnum.PB,
                "大模型平台类型不合法");
        if (param.getPlatformType() == PlatformTypeEnum.AI.getCode()) {
            CommonUtils.checkEval(StringUtils.isNotBlank(param.getWorkspaceId()), "AI搭-空间ID不能为空");
            CommonUtils.checkEval(StringUtils.isNotBlank(param.getApplicationId()), "AI搭-应用ID不能为空");
        }
    }

    @Override
    public List<ManualInspectResultDTO> getManualInspectResult(Integer dimension, String applicationId, String sessionId, String messageId, String llmMessageId) {
        CommonUtils.checkEval(dimension != null, "质检维度不能为空");
        CommonUtils.checkEval(StringUtils.isNotBlank(sessionId), "会话ID不能为空");
        CommonUtils.checkEval(DimensionTypeEnum.getByCode(dimension) != null, "质检维度不合法");
        if (dimension == DimensionTypeEnum.QUERY.getCode()) {
            CommonUtils.checkEval(StringUtils.isNotBlank(messageId), "外部业务消息ID不能为空");
            CommonUtils.checkEval(StringUtils.isNotBlank(llmMessageId), "aida消息ID不能为空");
            CommonUtils.checkEval(StringUtils.isNotBlank(applicationId), "Query维度仅支持aida平台, 不支持PB平台, aida应用id需要传递");
        }

        log.info(String.format("getManualInspectResult dimension:%s, sessionId:%s, messageId:%s, llmMessageId:%s, applicationId:%s", dimension, sessionId, messageId, llmMessageId, applicationId));
        List<WorkbenchInspectInfoPo> list;
        if (!dimension.equals(DimensionTypeEnum.QUERY.getCode())) {
            list = workbenchInspectInfoGeneratorService.getByCondition(WorkbenchInspectInfoConditionParam.builder()
                    .dimension(dimension)
                    .sessionId(sessionId)
                    .type(MetricEvalTypeEnum.MANUAL.getCode())
                    // 只有质检员可以看到所有评测数据
                    .creator(getCurrentUserRole() == WorkBenchRoleEnum.INSPECTOR ? null : UserUtils.getUser().getLogin())
                    .build());
        } else {
            list = workbenchInspectInfoGeneratorService.getByCondition(WorkbenchInspectInfoConditionParam.builder()
                    .dimension(dimension)
                    .sessionId(sessionId)
                    .messageId(messageId)
                    .llmMessageId(llmMessageId)
                    .applicationId(applicationId)
                    .type(MetricEvalTypeEnum.MANUAL.getCode())
                    // 只有质检员可以看到所有评测数据
                    .creator(getCurrentUserRole() == WorkBenchRoleEnum.INSPECTOR ? null : UserUtils.getUser().getLogin())
                    .build());
        }

        //  获取手动质检指标ID列表
        List<Long> manualMetricId = getManualMetricId(sessionId, applicationId);
        if (CollectionUtils.isEmpty(manualMetricId)) {
            return Collections.emptyList();
        }
        Map<Long, MetricConfigPo> metricConfigMap = getMetricConfigMap(manualMetricId);

        // 质检员可以看到所有评测数据
        if (getCurrentUserRole() == WorkBenchRoleEnum.INSPECTOR) {
            List<ManualInspectResultDTO> collect = list.stream().map(info -> buildManualInspectResultDTO(info.getMetricId(), metricConfigMap, info)).collect(Collectors.toList());
            Set<Long> longs = metricConfigMap.keySet();
            // 做差
            if (CollectionUtils.isNotEmpty(collect)) {
                longs.removeAll(collect.stream().map(ManualInspectResultDTO::getMetricId).collect(Collectors.toSet()));
            }
            // 组装未评测指标
            if (CollectionUtils.isNotEmpty(longs)) {
                collect.addAll(longs.stream().map(id -> buildManualInspectResultDTO(id, metricConfigMap, null)).collect(Collectors.toList()));
            }
            return collect;
        }

        Map<Long, WorkbenchInspectInfoPo> collect = list.stream().collect(Collectors.toMap(WorkbenchInspectInfoPo::getMetricId, Function.identity()));
        // 评测人可以看到自己的数据，包括未评测指标
        return metricConfigMap.keySet().stream().map(id -> buildManualInspectResultDTO(id, metricConfigMap, collect.get(id))).collect(Collectors.toList());
    }

    /**
     * 构建手动质检结果DTO
     *
     * @param id              指标ID
     * @param metricConfigMap 指标配置映射表
     * @param inspectInfo     质检信息
     * @return 手动质检结果DTO
     */
    private ManualInspectResultDTO buildManualInspectResultDTO(Long id, Map<Long, MetricConfigPo> metricConfigMap, WorkbenchInspectInfoPo inspectInfo) {
        ManualInspectResultDTO resultDTO = new ManualInspectResultDTO();
        resultDTO.setMetricId(id);
        if (metricConfigMap.get(id) != null) {
            resultDTO.setMetricName(metricConfigMap.get(id).getName());

            // 指标类型
            resultDTO.setMetricType(metricConfigMap.get(id).getMetricType());
            // 解析指标结果枚举列表
            JSONObject ranges = JSONObject.parseObject(metricConfigMap.get(id).getRanges());
            if (ranges.containsKey("resultEnum")) {
                resultDTO.setResultEnum(ranges.getObject("resultEnum", new TypeReference<List<String>>() {
                }));
                resultDTO.getResultEnum().sort(Comparator.naturalOrder());
            } else {
                log.warn("指标结果枚举列表为空, metric:{}", metricConfigMap.get(id));
            }
        }


        if (inspectInfo != null) {
            resultDTO.setInspectInfoId(inspectInfo.getId());
            resultDTO.setResult(JSONArray.parseArray(inspectInfo.getMetricResult(), String.class));
            resultDTO.setMis(inspectInfo.getUpdaterMis());
            resultDTO.setTime(DateUtil.format(inspectInfo.getGmtModified(), null));
            resultDTO.setResultNote(inspectInfo.getNote());
            resultDTO.setSessionId(inspectInfo.getSessionId());
            resultDTO.setMessageId(inspectInfo.getMessageId());
            resultDTO.setLlmMessageId(inspectInfo.getLlmMessageId());
            resultDTO.setPlatformApp(inspectInfo.getPlatformApp());
            resultDTO.setPlatformWorkspace(inspectInfo.getPlatformWorkspace());
        } else {
            resultDTO.setResult(Collections.emptyList());
        }
        return resultDTO;
    }

    /**
     * 获取自动指标ID列表
     *
     * @return 自动指标ID列表
     */
    @Override
    public List<Long> getAutoMetricId(String sessionId, String applicationId) {
        List<MetricsRelation> list = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SESSION_METRIC_RELATION, MetricsRelation.class);
        Map<String, MetricsRelation> collect = list.stream().collect(Collectors.toMap(MetricsRelation::getSceneTypeName, Function.identity()));
        if (StringUtils.isBlank(applicationId)) {
            String sceneTypeName = getSceneTypeNameBySessionId(sessionId);
            return sceneTypeName != null && collect.containsKey(sceneTypeName) ? collect.get(sceneTypeName).getMetricsRelation().stream().map(WorkbenchMetricsMappingDTO::getAutoMetricId).collect(Collectors.toList()) : Collections.emptyList();
        } else {
            return collect.containsKey(applicationId) ? collect.get(applicationId).getMetricsRelation().stream().map(WorkbenchMetricsMappingDTO::getAutoMetricId).collect(Collectors.toList()) : Collections.emptyList();
        }
    }


    @Override
    public Map<Long, MetricConfigPo> getMetricConfigMap(List<Long> metricIdList) {
        Map<Long, MetricConfigPo> collect = metricConfigGeneratorService.listByIds(metricIdList).stream().collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        Set<Long> set = collect.keySet();
        metricIdList.forEach(id -> CommonUtils.checkEval(set.contains(id), String.format("指标ID: %s 不存在", id)));
        return collect;
    }

    /**
     * 提交人工质检结果
     *
     * @param manualInspectParam 人工质检请求参数
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitManualInspectResult(ManualInspectParam manualInspectParam) {
        // 校验请求通用参数
        checkWorkbenchCommonParam(manualInspectParam);

        // 校验输入
        List<Long> manualMetricId = getManualMetricId(manualInspectParam.getSessionId(), manualInspectParam.getApplicationId());
        CommonUtils.checkEval(CollectionUtils.isNotEmpty(manualMetricId), "当前场景未配置人工质检指标");
        HashSet<Long> set = new HashSet<>(manualMetricId);
        manualInspectParam.getInspectResult().forEach(inspectResult -> {
            CommonUtils.checkEval(inspectResult.getMetricId() != null, "人工指标ID不能为空");
            CommonUtils.checkEval(set.contains(inspectResult.getMetricId()), String.format("人工指标ID: %s 不存在", inspectResult.getMetricId()));
            CommonUtils.checkEval(CollectionUtils.isNotEmpty(inspectResult.getResult()), "提交的人工质检结果不能为空");
        });

        // 保存会话信息到workbench_session_info表, 设置状态为已质检
        saveSessionInfo(manualInspectParam.getSessionId(), manualInspectParam.getPlatformType(), manualInspectParam.getLlmSessionId(),
                new WorkspaceAppDTO(manualInspectParam.getWorkspaceId(), manualInspectParam.getApplicationId()), YesNoEnum.YES);

        // 遍历质检结果，进行保存或更新操作
        manualInspectParam.getInspectResult().forEach(inspectResult -> {
            // 去重复
            if (inspectResult.getResult() == null) {
                inspectResult.setResult(new ArrayList<>());
            }
            inspectResult.setResult(new ArrayList<>(new HashSet<>(inspectResult.getResult())));
            inspectResult.getResult().sort(Comparator.naturalOrder());

            // 查询质检信息
            List<WorkbenchInspectInfoPo> inspectInfoList;

            if (!manualInspectParam.getDimension().equals(DimensionTypeEnum.QUERY.getCode())) {
                // 根据维度信息获取质检信息详情
                inspectInfoList = workbenchInspectInfoGeneratorService.getByCondition(WorkbenchInspectInfoConditionParam.builder()
                        .sessionId(manualInspectParam.getSessionId())
                        .metricId(inspectResult.getMetricId())
                        .type(MetricEvalTypeEnum.MANUAL.getCode())
                        .dimension(manualInspectParam.getDimension())
                        .creator(UserUtils.getUser().getLogin())
                        .build());
            } else {
                WorkbenchInspectInfoConditionParam condition;
                if (manualInspectParam.getPlatformType().equals(PlatformTypeEnum.AI.getCode())) {
                    condition = WorkbenchInspectInfoConditionParam.builder()
                            .sessionId(manualInspectParam.getSessionId())
                            .metricId(inspectResult.getMetricId())
                            .type(MetricEvalTypeEnum.MANUAL.getCode())
                            .dimension(manualInspectParam.getDimension())
                            .messageId(manualInspectParam.getMessageId())
                            .llmMessageId(manualInspectParam.getLlmMessageId())
                            .applicationId(manualInspectParam.getApplicationId())
                            .creator(UserUtils.getUser().getLogin())
                            .build();
                } else {
                    condition = WorkbenchInspectInfoConditionParam.builder()
                            .sessionId(manualInspectParam.getSessionId())
                            .metricId(inspectResult.getMetricId())
                            .type(MetricEvalTypeEnum.MANUAL.getCode())
                            .dimension(manualInspectParam.getDimension())
                            .messageId(manualInspectParam.getMessageId())
                            .llmMessageId(manualInspectParam.getLlmMessageId())
                            .creator(UserUtils.getUser().getLogin())
                            .build();
                }
                inspectInfoList = workbenchInspectInfoGeneratorService.getByCondition(condition);
            }

            if (CollectionUtils.isEmpty(inspectInfoList)) {
                // 如果质检信息不存在，则保存新的质检结果
                log.info("保存质检结果, mis: {}, session id: {}, 质检结果: {}", UserUtils.getUser().getLogin(), manualInspectParam.getSessionId(), inspectResult);
                saveWorkbenchInspectInfo(inspectResult, manualInspectParam);
            } else {
                // 只有一条
                WorkbenchInspectInfoPo inspectInfo = inspectInfoList.get(0);

                // 如果质检信息已存在，比较结果是否有变化，有变化则更新
                if (CollectionUtils.isEqualCollection(JSONArray.parseArray(inspectInfo.getMetricResult(), String.class), inspectResult.getResult()) && StringUtils.equals(inspectInfo.getNote(), inspectResult.getResultNote())) {
                    log.info("质检结果无变化, mis: {}, session id: {}, 质检结果: {}", UserUtils.getUser().getLogin(), manualInspectParam.getSessionId(), inspectResult);
                    return;
                }

                inspectInfo.setMetricResult(JSONArray.toJSONString(inspectResult.getResult()));
                inspectInfo.setGmtModified(new Date());
                inspectInfo.setUpdaterMis(UserUtils.getUser().getLogin());
                inspectInfo.setNote(inspectResult.getResultNote());
                workbenchInspectInfoGeneratorService.updateById(inspectInfo);
            }
        });
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adoptAutoInspectResult(WorkbenchAdoptParam param) {
        //  验证并获取质检信息列表
        List<WorkbenchInspectInfoPo> workbenchInspectInfoPos = validateAndFetchInspectInfoList(param);
        //  验证指标映射关系，确保自动指标ID和手动指标ID的映射关系正确无误
        Map<Long, Long> metricMapping = getMetricMapping();

        // 保存PB会话信息, 设置为已质检
        WorkbenchInspectInfoPo workbenchInspectInfoPo = workbenchInspectInfoPos.get(0);
        saveSessionInfo(workbenchInspectInfoPo.getSessionId(), workbenchInspectInfoPo.getPlatformType(),
                Arrays.asList(workbenchInspectInfoPo.getLlmSessionId().split(",")),
                new WorkspaceAppDTO(workbenchInspectInfoPo.getPlatformWorkspace(), workbenchInspectInfoPo.getPlatformApp()),
                YesNoEnum.YES);
        //  处理质检信息，根据自动指标ID和手动指标ID的映射关系，将自动质检结果转换为手动质检结果，并保存或更新到数据库中。
        workbenchInspectInfoPos.removeIf(info -> {
            if (!metricMapping.containsKey(info.getMetricId())) {
                log.warn("没有找到对应的指标映射关系param: {} , info: {}", param, info);
                return true;
            }
            processInspectInfo(info, metricMapping.get(info.getMetricId()));
            return false;
        });
        //  更新质检信息的采纳状态
        updateAdoptedStatus(workbenchInspectInfoPos);
    }


    @Override
    public List<WorkbenchMetricsMappingDTO> getWorkbenchMetricsMappingDTOList() {
        Map<Long, Long> metricMapping = getMetricMapping();
        return metricMapping.entrySet().stream()
                .map(entry -> new WorkbenchMetricsMappingDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 从缓存取数据
     *
     * @param sessionId
     * @return
     */
    private List<InspectWorkbenchDTO> getSessionDetailFromCache(String sessionId) {
        try {
            String sessionCache = redisClientProxy.get(StoreKey.valueOf(RedisConstants.WORKBENCH_SESSION_DETAIL_CACHE, sessionId));
            if (StringUtils.isNotBlank(sessionCache)) {
                return JSON.parseArray(sessionCache, InspectWorkbenchDTO.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取对话详情异常, sessionId:{}", sessionId, e);
        }

        return Lists.newArrayList();
    }

    /**
     * 输入sessionId分页查询会话session
     * 1. 先看pb
     * 2. 再查aida
     *
     * @param condition
     * @return
     */
    private PageData<InspectWorkbenchDTO> pageSessionWithSessionId(PageParam<InspectWorkbenchConditionParam> condition) {
        PageData<InspectWorkbenchDTO> pageData = PageData.create(0, condition.getPageNum(), condition.getPageSize(), Collections.emptyList());

        String sessionId = condition.getCondition().getSessionId();

        List<InspectWorkbenchDTO> sessionDetailFromCache = getSessionDetailFromCache(sessionId);
        if (CollectionUtils.isNotEmpty(sessionDetailFromCache)) {
            pageData.setData(sessionDetailFromCache);
            return PageData.create(sessionDetailFromCache.size(), condition.getPageNum(), condition.getPageSize(), sessionDetailFromCache);
        }

        // 有sessionId时，结果一定只有一条，可以进行一些优化
        // 优化点：不需要联conversation表一起查，只需要找到任意一条message中sessionId中能对上的，每次只需要查询一条
        // 剩余的条件，查询结束后做二次校验，如果能匹配，返回结果，如果不匹配，返回空 (全部交给数据库了)
        // 设置查询Ai搭数据的日期范围，根据Lion配置的最大查询天数进行设置
        Pair<Date, Date> datePair = operationAnalysisHelper.calculateInspectTime(condition.getCondition().getStartTime(), condition.getCondition().getEndTime());
        condition.getCondition().setEndTime(datePair.getRight());
        condition.getCondition().setStartTime(datePair.getLeft());

        // 将InspectWorkbenchConditionParam查询条件转化为AidaMessagesConditionParam查询条件
        AidaMessagesConditionParam param = operationAnalysisHelper.convertInspectWorkbenchConditionParam(condition.getCondition(), UserUtils.getUser().getLogin());

        // 到aidaMessages表中按照其他筛选条件分页查询
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<AidaMessagesSessionGroupResultParam> aidaMessagesSessionGroupResultParams = aidaMessagesGeneratorService.listSessionGroupByConditionAndInSessionIds(param, Sets.newHashSet(condition.getCondition().getSessionId()));
        log.info("pageSessionWithSessionId, param:{} aidaMessagesSessionGroupResultParams: {}", JSON.toJSONString(param),
                JSON.toJSONString(aidaMessagesSessionGroupResultParams));
        PageInfo<AidaMessagesSessionGroupResultParam> pageInfo = new PageInfo<>(aidaMessagesSessionGroupResultParams);
        PageHelper.clearPage();

        pageData = operationAnalysisHelper.listInspectWorkbenchBySessionIds(pageInfo);

        try {
            redisClientProxy.set(StoreKey.valueOf(RedisConstants.WORKBENCH_SESSION_DETAIL_CACHE, sessionId), JSON.toJSONString(pageData.getData()));
        } catch (Exception e) {
            log.error("缓存set对话详情异常, sessionId:{}", sessionId, e);
        }
        return pageData;
    }

    /**
     * 筛选条件包含已质检前提下，根据pb还是aida分流查询信息
     *
     * @param condition 一般查询条件
     * @return 分页后的信息
     */
    private PageData<InspectWorkbenchDTO> pageSessionWithInspectionStatusWithPbAidaTraffic(PageParam<InspectWorkbenchConditionParam> condition) {
        CommonUtils.checkEval(YesNoEnum.YES.getCode().equals(condition.getCondition().getInspectionStatus()), "仅支持已质检状态查询");
        return pageSessionWithInspectionStatusWithAida(condition);
    }

    /**
     * 筛选条件包含已质检前提下，按照aida的平台统计逻辑返回数据
     *
     * @param condition 一般查询条件
     * @return 分页后的信息
     */
    private PageData<InspectWorkbenchDTO> pageSessionWithInspectionStatusWithAida(PageParam<InspectWorkbenchConditionParam> condition) {
        // 先到质检详情表中筛选sessionIds
        Set<String> sessionIdSet = workbenchInspectInfoGeneratorService.listSessionIdsByDimensionAndLLMMessageIdNotNullAndTypeAndAidaPlatform(DimensionTypeEnum.QUERY.getCode(), MetricEvalTypeEnum.MANUAL.getCode());
        if (CollectionUtils.isEmpty(sessionIdSet)) {
            return PageData.create(0, condition.getPageNum(), condition.getPageSize(), Collections.emptyList());
        }

        // 查看condition中是否存在sessionId
        String sessionIdSpecify = condition.getCondition().getSessionId();
        if (StringUtils.isNotBlank(sessionIdSpecify)) {
            sessionIdSet.retainAll(Sets.newHashSet(sessionIdSpecify));
        }

        if (CollectionUtils.isEmpty(sessionIdSet)) {
            return PageData.create(0, condition.getPageNum(), condition.getPageSize(), Collections.emptyList());
        }

        // 将InspectWorkbenchConditionParam查询条件转化为AidaMessagesConditionParam查询条件
        AidaMessagesConditionParam param = operationAnalysisHelper.convertInspectWorkbenchConditionParam(condition.getCondition(), UserUtils.getUser().getLogin());

        // 到aidaMessages表中按照其他筛选条件分页查询
        // 构造分页
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<AidaMessagesSessionGroupResultParam> aidaMessagesSessionGroupResultParams = aidaMessagesGeneratorService.listSessionGroupByConditionAndInSessionIds(param, sessionIdSet);
        PageInfo<AidaMessagesSessionGroupResultParam> pageInfo = new PageInfo<>(aidaMessagesSessionGroupResultParams);
        PageHelper.clearPage();

        return operationAnalysisHelper.listInspectWorkbenchBySessionIds(pageInfo);
    }

    private PageData<InspectWorkbenchDTO> pageSessionWithNoSessionId(PageParam<InspectWorkbenchConditionParam> condition) {
        // 如果没有输入任何条件，返回空，因为数据量过大，查询速度慢
        if (!hasCondition(condition.getCondition())) {
            return PageData.create(0, condition.getPageNum(), condition.getPageSize(), new ArrayList<>());
        }

        // 查询Aida 获取策略
        Integer strategy = Lion.getInt(ConfigUtil.getAppkey(), "workbench.strategy.config", WorkbenchQueryStrategyEnum.AIDA_DATABASE.getCode());
        log.info("pageSessionWithNoSessionId strategy:{}, {}", strategy, WorkbenchQueryStrategyEnum.getByCode(strategy));

        // 设置查询条件中的应用ID列表，根据条件中的应用ID是否为空进行设置，如果为空查询所有配置上去的应用，并包装单个AppID
        if (!Objects.equals(WorkbenchQueryStrategyEnum.EVAL_DATABASE.getCode(), strategy)) {
            // 若查询策略非“评测数据库”则按原逻辑处理
            condition.getCondition().setApplicationIdList(StringUtils.isNotBlank(condition.getCondition().getApplicationId()) ? Collections.singletonList(condition.getCondition().getApplicationId()) : getAllAidaAppId());
        }

        // 设置查询Ai搭数据的日期范围，根据Lion配置的最大查询天数进行设置
        Pair<Date, Date> datePair = operationAnalysisHelper.calculateInspectTime(condition.getCondition().getStartTime(), condition.getCondition().getEndTime());
        condition.getCondition().setEndTime(datePair.getRight());
        condition.getCondition().setStartTime(datePair.getLeft());

        // 开始查询
        log.info("开始分页查询Aida数据：condition = {}", JSONObject.toJSONString(condition));
        return workbenchQueryStrategyServiceMap.get(strategy).pageSession(condition);
    }

    /**
     * 获取所有可以选择的Ai搭应用ID
     *
     * @return Ai搭应用ID列表
     */
    private List<String> getAllAidaAppId() {
        return getWorkspaceAppInfo().stream().flatMap(workspaceApp -> workspaceApp.getApplicationList().stream()).map(WorkspaceAppDTO.Application::getApplicationId).collect(Collectors.toList());
    }

    private UserInfo getPbUserInfo(String sessionId) {
        DialogSessionConditionParam param = new DialogSessionConditionParam();
        param.setSessionId(sessionId);
        List<DialogTraceDTO> requestTraceList = pbServiceProxy.getAllRequestTrace(sessionId);
        UserInfo userInfo = new UserInfo();
        // 从trace信息里面获取获取用户ID和类型
        if (CollectionUtils.isNotEmpty(requestTraceList)) {
            // 过滤得到存放入参的trace，用户信息通过入参传入
            DialogTraceDTO startTrace = requestTraceList.get(0);
            if (startTrace != null && StringUtils.isNotBlank(startTrace.getInput())) {
                DialogRequest dialogRequest = JSONObject.parseObject(startTrace.getInput(), DialogRequest.class);
                userInfo.setUserId(dialogRequest.getUserId());
                userInfo.setUserType(dialogRequest.getUserType());
            }
        }
        return userInfo;
    }

    /**
     * 从评测库 获取Ai搭消息详情
     *
     * @param param
     * @return
     */
    private InspectWorkbenchQueryDetailDTO getAidaMessageDetailFromEval(InspectWorkbenchDetailParam param) {
        AidaMessagesConditionParam condition = new AidaMessagesConditionParam();
        condition.setSessionId(param.getSessionId());

        List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByCondition(condition);
        if (CollectionUtils.isEmpty(aidaMessagesPoList)) {
            return null;
        }
        log.info("getAidaMessageDetailFromEval listByCondition, param:{}, result:{}", JSON.toJSONString(condition),
                JSON.toJSONString(aidaMessagesPoList));
        // 匹配大模型消息id
        AidaMessagesPo matchMessage = null;
        for (AidaMessagesPo aidaMessagesPo : aidaMessagesPoList) {
            if (StringUtils.isNotEmpty(param.getLlmMessageId()) && param.getLlmMessageId().equals(aidaMessagesPo.getId())) {
                matchMessage = aidaMessagesPo;
                break;
            }
        }
        log.info("getAidaMessageDetailFromEval matchMessage, matchMessage:{}", matchMessage != null ? JSON.toJSONString(matchMessage) : "empty");

        return buildAidaQueryDetailDTO(param, aidaMessagesPoList, matchMessage);
    }

    /**
     * 从评测库 获取应用维度的Ai搭执行链路信息
     *
     * @param param
     * @return
     */
    private InspectWorkbenchQueryDetailPathDTO getAidaAppExecutionPathFromEval(InspectWorkbenchDetailPathParam param) {
        AidaMessagesConditionParam condition = new AidaMessagesConditionParam();
        condition.setSessionId(param.getSessionId());

        List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByCondition(condition);
        if (CollectionUtils.isEmpty(aidaMessagesPoList)) {
            return null;
        }
        // 匹配大模型消息id
        AidaMessagesPo matchMessage = null;
        for (AidaMessagesPo aidaMessagesPo : aidaMessagesPoList) {
            if (StringUtils.isNotEmpty(param.getLlmMessageId()) && param.getLlmMessageId().equals(aidaMessagesPo.getId())) {
                matchMessage = aidaMessagesPo;
                break;
            }
        }

        VersionConfigDTO versionConfigDTO = null;
        try {
            versionConfigDTO = inspectWorkbenchAidaRemoteService.versionConfigByVersionId(param.getApplicationVersionId());
        } catch (Exception e) {
            log.error("getAidaAppExecutionPathFromEval error", e);
            throw new RuntimeException("获取Ai搭应用版本配置失败", e);
        }


        return buildAidaQueryDetailPathDTO(param, aidaMessagesPoList, matchMessage, versionConfigDTO);

    }

    private InspectWorkbenchQueryDetailPathDTO buildAidaQueryDetailPathDTO(InspectWorkbenchDetailPathParam param, List<AidaMessagesPo> messageList, AidaMessagesPo matchMessage, VersionConfigDTO versionConfigDTO) {
        CommonUtils.checkEval(matchMessage != null, "未获取到会话记录，请稍后重试");

        InspectWorkbenchQueryDetailPathDTO inspectWorkbenchQueryDetailPathDTO = new InspectWorkbenchQueryDetailPathDTO();
        /**
         * 1. 根据messageList, matchMessage查出所有日志记录
         * 2. 根据filterAppId过滤日志记录（只保留filterAppId的日志记录）
         */
        inspectWorkbenchQueryDetailPathDTO.setAidaTraceList(buildAidaTraceList(messageList, matchMessage, param.getFilterAppId()));
        JSONObject nodeConfigJsonObject = new JSONObject();
        if (StringUtils.isNotBlank(versionConfigDTO.getJson())) {
            nodeConfigJsonObject = JSONObject.parseObject(versionConfigDTO.getJson());
        }
        inspectWorkbenchQueryDetailPathDTO.setNodeConfig(nodeConfigJsonObject);
        inspectWorkbenchQueryDetailPathDTO.setNodeReferenceInfo(buildNodeReferenceInfo(versionConfigDTO));
        inspectWorkbenchQueryDetailPathDTO.setAppName(versionConfigDTO.getAppName());
        inspectWorkbenchQueryDetailPathDTO.setVersionName(versionConfigDTO.getVersionName());

        return inspectWorkbenchQueryDetailPathDTO;
    }

    private List<InspectWorkbenchQueryDetailPathDTO.NodeReferenceInfo> buildNodeReferenceInfo(VersionConfigDTO versionConfigDTO) {
        String bizConfig = versionConfigDTO.getBizConfig();
        if (StringUtils.isBlank(bizConfig)) {
            return Collections.emptyList();
        }

        List<InspectWorkbenchQueryDetailPathDTO.NodeReferenceInfo> result = new ArrayList<>();

        try {
            JSONObject bizConfigJson = JSONObject.parseObject(bizConfig);

            for (Map.Entry<String, Object> entry : bizConfigJson.entrySet()) {
                String nodeId = entry.getKey();

                InspectWorkbenchQueryDetailPathDTO.NodeReferenceInfo nodeReferenceInfo = new InspectWorkbenchQueryDetailPathDTO.NodeReferenceInfo();
                nodeReferenceInfo.setNodeId(nodeId);

                // 先保证节点类型为llm，保证config可序列化，再验证config配置
                String nodeType = ((JSONObject) entry.getValue()).getString("type");
                if (StringUtils.isBlank(nodeType) || !"llm".equals(nodeType)) {
                    nodeReferenceInfo.setReferAppId(null);
                    nodeReferenceInfo.setReferVersionId(null);
                    result.add(nodeReferenceInfo);
                    continue;
                }

                JSONObject nodeConfig = ((JSONObject) entry.getValue()).getJSONObject("config");
                if (nodeConfig == null || !"rule".equals(nodeConfig.getString("app_mode")) || !"refer".equals(nodeConfig.getString("source"))) {
                    nodeReferenceInfo.setReferAppId(null);
                    nodeReferenceInfo.setReferVersionId(null);
                    result.add(nodeReferenceInfo);
                    continue;
                }

                nodeReferenceInfo.setReferAppId(nodeConfig.getString("id"));
                nodeReferenceInfo.setReferVersionId(nodeConfig.getString("app_model_version_id"));
                result.add(nodeReferenceInfo);
            }
        } catch (Exception e) {
            log.error("构建节点引用关系失败,param={},msg={}", JSON.toJSONString(versionConfigDTO), e.getMessage(), e);
            return Collections.emptyList();
        }

        return result;
    }

    private List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> buildAidaTraceList(List<AidaMessagesPo> messageList, AidaMessagesPo matchMessage, String filterAppId) {
        Map<String, List<AidaMessagesPo>> messageMap = new HashMap<>();
        for (AidaMessagesPo messageDTO : messageList) {
            if (StringUtils.isBlank(messageDTO.getCommonData())) {
                continue;
            }
            JSONObject commonData = JSONObject.parseObject(messageDTO.getCommonData());
            String traceId = commonData.getString("trace_id");
            messageMap.computeIfAbsent(traceId, k -> new ArrayList<>()).add(messageDTO);
        }
        JSONObject commonData = JSONObject.parseObject(matchMessage.getCommonData());
        String traceId = commonData.getString("trace_id");
        Map<String, String> messageIdApplicationIdMap = new HashMap<>();
        if (StringUtils.isNotBlank(traceId)) {
            messageIdApplicationIdMap = messageMap.get(traceId).stream().collect(Collectors.toMap(AidaMessagesPo::getId, AidaMessagesPo::getAppId));
        }
        messageIdApplicationIdMap.put(matchMessage.getId(), matchMessage.getAppId());
        List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList = new ArrayList<>();
        for (Map.Entry<String, String> entry : messageIdApplicationIdMap.entrySet()) {
            if (StringUtils.isNotBlank(filterAppId) && !filterAppId.equals(entry.getValue())) {
                continue;
            }
            // 查询大模型节点的日志信息
            List<WorkflowNodeExeDetailsDTO> curMessageLogList = aidaInvokeServiceProxy.getWorkFlowNodeLogList(entry.getKey());

            if (CollectionUtils.isNotEmpty(curMessageLogList)) {
                logList.addAll(buildAidaTraceList(curMessageLogList, entry.getValue()));
            }
        }
        logList.sort(Comparator.comparing(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getStartTime));
        return logList;
    }

    @Override
    public InspectWorkbenchQueryDetailDTO buildAidaQueryDetailDTO(InspectWorkbenchDetailParam param, List<AidaMessagesPo> messageList, AidaMessagesPo matchMessage) {
        // 根据前端传参找到匹配的消息
        if (matchMessage == null) {
            // 判断渠道信息
            AidaMessagesPo firstMessage = messageList.get(0);
            JSONObject extraInfoJson = JSON.parseObject(firstMessage.getExtraInfo());
            String channel = extraInfoJson.getString(WorkbenchConstants.CHANNEL);
            if (StringUtils.isNotEmpty(channel) && ChannelEnum.IVR.getCode().equals(channel)) {
                matchMessage = getMatchMessageFromIvr(messageList, param);
            } else {
                matchMessage = getMatchMessageFromOnline(messageList, param);
            }
            log.info("buildAidaQueryDetailDTO, param:{}, matchMessage:{}", JSON.toJSONString(param),
                    matchMessage != null ? JSON.toJSONString(matchMessage) : "empty");
        }

        CommonUtils.checkEval(matchMessage != null, "未获取到会话记录，请稍后重试");

        // 获取自定义配置
        WorkbenchCustomConfigCondition customConfigCondition = new WorkbenchCustomConfigCondition();
        customConfigCondition.setPlatformWorkspace(param.getWorkspaceId());
        customConfigCondition.setAppId(matchMessage.getAppId());
        customConfigCondition.setAppVersionId(AidaMessageUtil.getApplicationVersion(matchMessage.getCommonData()));
        customConfigCondition.setCreatorMis(UserUtils.getUser().getLogin());
        Map<String, String> customConfigMap = workbenchCustomConfigGeneratorService.getListByCondition(customConfigCondition).stream()
                .collect(Collectors.toMap(WorkbenchCustomConfigPo::getType, WorkbenchCustomConfigPo::getConfig, (a, b) -> a));

        InspectWorkbenchQueryDetailDTO inspectWorkbenchQueryDetail = new InspectWorkbenchQueryDetailDTO();
        inspectWorkbenchQueryDetail.setId(matchMessage.getId());
        // 获取全部的调用日志，包括子流程
        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> nodeMap = getNodeExeDetailsList(messageList, matchMessage);
        List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList = nodeMap.get("logList");
        // 构造以大模型节点分组的信号信息
        inspectWorkbenchQueryDetail.setSignal(buildGroupSignal(param, logList, customConfigMap));
        // 合并结果，构造所有AI搭信号信息
        inspectWorkbenchQueryDetail.setAllSignal(buildAllSignal(inspectWorkbenchQueryDetail.getSignal(), customConfigMap));
        // 构造基本的信号信息，机器质检回调使用
        inspectWorkbenchQueryDetail.setSignalList(buildSignalList(inspectWorkbenchQueryDetail.getAllSignal()));
        inspectWorkbenchQueryDetail.setDetail(buildDetailInfo(logList));
        inspectWorkbenchQueryDetail.setAidaTraceList(nodeMap.get("aidaTraceList"));
        inspectWorkbenchQueryDetail.setSelectedNodeId(buildSelectedNodeId(customConfigMap, inspectWorkbenchQueryDetail));

        // 根据是否是AI平台, 添加消息层面的aida应用信息
        if (Objects.equals(param.getPlatform(), PlatformTypeEnum.AI.getCode())) {
            operationAnalysisHelper.addMessageLevelAidaAppInfoToInspectWorkbenchQueryDetailDTO(inspectWorkbenchQueryDetail, matchMessage);
        }
        return inspectWorkbenchQueryDetail;
    }

    @Override
    public InspectWorkbenchQueryLlmTreeDTO queryLlmTree(InspectWorkbenchDetailParam param) {
        InspectWorkbenchQueryLlmTreeDTO inspectWorkbenchQueryLlmTreeDTO = new InspectWorkbenchQueryLlmTreeDTO();
        List<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> aidaLlmTreeList = new ArrayList<>();
        try {
            AidaMessagesConditionParam condition = new AidaMessagesConditionParam();
            condition.setSessionId(param.getSessionId());
            //  使用唯一消息ID
            List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByCondition(condition);

            AidaMessagesPo aidaMessagesPo = aidaMessagesPoList.stream()
                    .filter(message -> StringUtils.equals(message.getId(), param.getLlmMessageId()))
                    .findFirst()
                    .orElse(null);
            CommonUtils.checkEval(aidaMessagesPo != null, "未获取到大模型消息，请稍后重试");

            Map<String, String> appIdVersionMap = aidaMessagesPoList.stream()
                    .filter(messagesPo -> StringUtils.isNotBlank(messagesPo.getCommonData()))
                    //  过滤出与指定消息具有相同traceId的消息列表
                    .filter(messagesPo -> StringUtils.equals(getMessageTraceId(messagesPo), getMessageTraceId(aidaMessagesPo)))
                    .filter(messagesPo -> StringUtils.isNotBlank(messagesPo.getAppId()))
                    .filter(messagesPo -> StringUtils.isNotBlank(messagesPo.getAppModelVersionId()))
                    .collect(Collectors.toMap(AidaMessagesPo::getAppId, AidaMessagesPo::getAppModelVersionId, (a, b) -> a));

            String applicationVersion = AidaMessageUtil.getApplicationVersion(aidaMessagesPo.getCommonData());
            CommonUtils.checkEval(StringUtils.isNotBlank(applicationVersion), "未获取到模型版本，请稍后重试");
            // 异步执行两个方法
            CompletableFuture<List<AidaAppTreeDTO>> aidaAppTreeDTOListFuture = CompletableFuture.supplyAsync(() -> aidaExecuteService.getAidaRobotTree(param.getApplicationId(), applicationVersion, appIdVersionMap), AIDA_SUB_APPLICATION_POOL);

            CompletableFuture<List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> aidaTraceListFuture =
                    CompletableFuture.supplyAsync(() -> {
                        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> aidaTraceMap = getAidaTraceMap(aidaMessagesPoList, Lists.newArrayList(param.getLlmMessageId()));
                        if (MapUtils.isNotEmpty(aidaTraceMap)) {
                            return aidaTraceMap.getOrDefault(param.getLlmMessageId(), Lists.newArrayList());
                        } else {
                            return Lists.newArrayList();
                        }
                    }, AIDA_SUB_APPLICATION_POOL);

            // 获取结果
            List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> aidaTraceList = aidaTraceListFuture.get(5, TimeUnit.SECONDS);
            List<AidaAppTreeDTO> appTreeDTOList = aidaAppTreeDTOListFuture.get(5, TimeUnit.SECONDS);

            if (CollectionUtils.isNotEmpty(appTreeDTOList)) {
                InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree aidaLlmTree = convertAidaLlmTree(appTreeDTOList, aidaTraceList);
                aidaLlmTreeList.add(aidaLlmTree);
            }

            // 获取自定义配置
            WorkbenchCustomConfigCondition customConfigCondition = new WorkbenchCustomConfigCondition();
            customConfigCondition.setPlatformWorkspace(param.getWorkspaceId());
            customConfigCondition.setAppId(aidaMessagesPo.getAppId());
            customConfigCondition.setAppVersionId(AidaMessageUtil.getApplicationVersion(aidaMessagesPo.getCommonData()));
            customConfigCondition.setCreatorMis(UserUtils.getUser().getLogin());
            customConfigCondition.setType(CustomConfigTypeEnum.SELECTED_LLM_TREE.getCode());
            WorkbenchCustomConfigPo workbenchCustomConfigPo = workbenchCustomConfigGeneratorService.getOneByCondition(customConfigCondition);
            String selectedAppId = Optional.ofNullable(workbenchCustomConfigPo)
                    .map(WorkbenchCustomConfigPo::getConfig)
                    .orElse(null);
            selectedAppId = filterSelectedAppId(aidaLlmTreeList, selectedAppId);
            inspectWorkbenchQueryLlmTreeDTO.setSelectedAppId(selectedAppId);
            inspectWorkbenchQueryLlmTreeDTO.setAidaLlmTree(aidaLlmTreeList);
            return inspectWorkbenchQueryLlmTreeDTO;
        } catch (Exception e) {
            Cat.logErrorWithCategory(CatConstants.WORKBENCH, new EvalException(e));
            log.error("获取模型调试信息树形结构失败,param={},msg={}", JSON.toJSONString(param), e.getMessage(), e);
        }
        return inspectWorkbenchQueryLlmTreeDTO;
    }


    /***
     * 变更信号排序接口
     * @param param
     */
    @Override
    public void signalReorder(SignalReorderParam param) {
        // 识别排序类型
        CustomConfigTypeEnum type = CustomConfigTypeEnum.SIGNAL_NODE_SORT;
        if (StringUtils.isBlank(param.getNodeId())) {
            type = CustomConfigTypeEnum.SIGNAL_ALL_SORT;
        }

        String login = UserUtils.getUser().getLogin();

        // 查询排序配置
        WorkbenchCustomConfigCondition condition = buildWorkbenchCustomConfigCondition(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), type, login);
        WorkbenchCustomConfigPo workbenchCustomConfigPo = workbenchCustomConfigGeneratorService.getOneByCondition(condition);

        String sourceConfig = Optional.ofNullable(workbenchCustomConfigPo).map(WorkbenchCustomConfigPo::getConfig).orElse(null);
        String config = reBuildConfig(param.getNodeId(), sourceConfig, param.getConfig(), type);

        // 保存或更新配置
        saveOrUpdateCustomConfig(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), config, workbenchCustomConfigPo, login, type);
    }

    /***
     * 变更节点选择接口
     * @param param
     * @return
     */
    @Override
    public void nodeSelected(NodeSelectedParam param) {
        // 配置类型
        CustomConfigTypeEnum type = CustomConfigTypeEnum.SELECTED_NODE;
        String nodeId = StringUtils.defaultIfBlank(param.getNodeId(), "");

        String login = UserUtils.getUser().getLogin();

        // 查询排序配置
        WorkbenchCustomConfigCondition condition = buildWorkbenchCustomConfigCondition(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), type, login);
        WorkbenchCustomConfigPo workbenchCustomConfigPo = workbenchCustomConfigGeneratorService.getOneByCondition(condition);

        // 保存或更新配置
        saveOrUpdateCustomConfig(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), nodeId, workbenchCustomConfigPo, login, type);
    }

    @Override
    public void llmSelected(LlmSelectedParam param) {
        // 配置类型
        CustomConfigTypeEnum type = CustomConfigTypeEnum.SELECTED_LLM_TREE;
        String selectedAppId = StringUtils.defaultIfBlank(param.getSelectedAppId(), "");

        String login = UserUtils.getUser().getLogin();

        // 查询排序配置
        WorkbenchCustomConfigCondition condition = buildWorkbenchCustomConfigCondition(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), type, login);
        WorkbenchCustomConfigPo workbenchCustomConfigPo = workbenchCustomConfigGeneratorService.getOneByCondition(condition);

        // 保存或更新配置
        saveOrUpdateCustomConfig(param.getAppId(), param.getPlatformWorkspace(), param.getAppVersionId(), selectedAppId, workbenchCustomConfigPo, login, type);
    }

    /***
     * 调试模型节点
     * @param param
     * @return
     */
    @Override
    public GptReplyDTO llmDebug(WorkbenchLlmDebugParam param) {
        InnerAppConfigDTO aidaRobotInfo = aidaInvokeServiceProxy.getAidaRobotInfo(param.getAppId(), param.getAidaAppVersionId());
        Boolean appOpenStreaming = aidaRobotInfo.getAppOpenStreaming() == null ? Boolean.FALSE : aidaRobotInfo.getAppOpenStreaming();
        GptRequestDTO gptRequest = buildGptRequestDTO(param);
        return commonEvalStrategyService.getGptResult(gptRequest, appOpenStreaming);
    }

    @Override
    public void saveReferenceDebuggingWindowWidth(Integer totalWidth, Integer referenceWindowWidth) {
        if (Objects.isNull(totalWidth) || Objects.isNull(referenceWindowWidth)) {
            log.warn("保存参考调试窗口宽度设置, 参数为空, totalWidth: {}, referenceWindowWidth: {}", totalWidth, referenceWindowWidth);
            throw new EvalException("保存参考调试窗口宽度设置参数为空");
        }
        ReferenceDebuggingWindowWidthSetUpDTO windowWidthSetUpDTO = ReferenceDebuggingWindowWidthSetUpDTO.builder()
                .referenceWindowWidth(referenceWindowWidth)
                .totalWidth(totalWidth)
                .build();
        String type = CustomConfigTypeEnum.REFERENCE_DEBUGGING_WINDOW_WIDTH.getCode();
        String userMis = Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse("");
        WorkbenchCustomConfigCondition customConfigCondition = new WorkbenchCustomConfigCondition();
        customConfigCondition.setCreatorMis(userMis);
        customConfigCondition.setType(type);
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<WorkbenchCustomConfigPo> workbenchCustomConfigList = workbenchCustomConfigGeneratorService.listByCondition(customConfigCondition);
        ZebraForceMasterHelper.clearLocalContext();
        WorkbenchCustomConfigPo workbenchCustomConfigPo = null;
        if (CollectionUtils.isNotEmpty(workbenchCustomConfigList)) {
            workbenchCustomConfigPo = workbenchCustomConfigList.get(0);
        }
        saveOrUpdateCustomConfig("", "", "", JSON.toJSONString(windowWidthSetUpDTO), workbenchCustomConfigPo, userMis, CustomConfigTypeEnum.REFERENCE_DEBUGGING_WINDOW_WIDTH);
    }

    /**
     * 获取录音详情
     *
     * @param contactId
     * @return
     */
    @Override
    public List<QueryRecordDataDTO> getCallOutInfo(String contactId) {
        return recordQueryServiceProxy.queryDualRecordData(contactId);
    }

    private AidaMessagesPo getMatchMessageFromIvr(List<AidaMessagesPo> messagesList, InspectWorkbenchDetailParam param) {
        // 解析透传给到前端的信息
        String originMessage = param.getOriginMessage();
        UserInteractiveInfoDTO.OperationDTO operationDTO = JSONObject.parseObject(originMessage, UserInteractiveInfoDTO.OperationDTO.class);
        JSONObject extraMapJson = JSONObject.parseObject(operationDTO.getExtraMap());
        if (extraMapJson == null) {
            return null;
        }
        // 只解析大模型消息类型
        if (!OperationTypeEnum.VOICE_BROADCAST.getCode().equals(operationDTO.getOperationTypeCode())) {
            return null;
        }
        VoiceBroadcaseDTO voiceBroadcaseDTO = JSONObject.parseObject(operationDTO.getExtraMap(), VoiceBroadcaseDTO.class);
        long timestamp = operationDTO.getOperationStartTime().getTime();
        String answer = voiceBroadcaseDTO.getVoiceTxt();

        // 优先取时间最接近且回复内容相等的回复
        AidaMessagesPo matchMessage = messagesList.stream()
                .filter(message -> StringUtils.isNotBlank(param.getApplicationId()) && message.getAppId().equals(param.getApplicationId()))
                .filter(message -> StringUtils.isNotBlank(message.getAnswer()) && message.getAnswer().contains(answer))
                .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt().getTime() - timestamp)))
                .orElse(null);
        // 如果回复内容没有相等的，取时间最接近且小于等于当前时间的
        if (matchMessage == null) {
            matchMessage = messagesList.stream()
                    .filter(message -> StringUtils.isNotBlank(param.getApplicationId()) && message.getAppId().equals(param.getApplicationId()))
                    .filter(message -> message.getCreatedAt().getTime() <= timestamp)
                    .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt().getTime() - timestamp)))
                    .orElse(null);
        }
        return matchMessage;
    }

    private AidaMessagesPo getMatchMessageFromOnline(List<AidaMessagesPo> messagesList, InspectWorkbenchDetailParam param) {
        // 解析透传给到前端的信息
        String originMessage = param.getOriginMessage();
        MessageDTO messageDTO = JSONObject.parseObject(originMessage, MessageDTO.class);
        JSONObject jsonObject = JSONObject.parseObject(messageDTO.getData());
        long timestamp = messageDTO.getAddTime() != null ? messageDTO.getAddTime().getTime() : 0L;
        String answer = jsonObject.containsKey("answer") ? jsonObject.getString("answer") : jsonObject.getString("content");

        if (jsonObject.containsKey("extendInfo")) {
            JSONObject extendInfo = jsonObject.getJSONObject("extendInfo");
            if (extendInfo.containsKey("triggerTime")) {
                timestamp = extendInfo.getLongValue("triggerTime");
            }
        }
        // 优先取时间最接近且回复内容相等的回复
        long finalTimestamp = timestamp;
        // 优先取时间最接近且回复内容相等的回复
        AidaMessagesPo matchMessage = messagesList.stream()
                .filter(message -> StringUtils.isNotBlank(param.getApplicationId()) && message.getAppId().equals(param.getApplicationId()))
                .filter(message -> StringUtils.isNotBlank(message.getAnswer()) && message.getAnswer().contains(answer))
                .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt().getTime() - finalTimestamp)))
                .orElse(null);
        // 如果回复内容没有相等的，取时间最接近且小于等于当前时间的
        if (matchMessage == null) {
            matchMessage = messagesList.stream()
                    .filter(message -> StringUtils.isNotBlank(param.getApplicationId()) && message.getAppId().equals(param.getApplicationId()))
                    .filter(message -> message.getCreatedAt().getTime() <= finalTimestamp)
                    .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt().getTime() - finalTimestamp)))
                    .orElse(null);
        }
        return matchMessage;
    }

    private Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> getNodeExeDetailsList(List<AidaMessagesPo> messageList, AidaMessagesPo message) {
        Map<String, List<AidaMessagesPo>> messageMap = new HashMap<>();
        for (AidaMessagesPo messageDTO : messageList) {
            if (StringUtils.isBlank(messageDTO.getCommonData())) {
                continue;
            }
            JSONObject commonData = JSONObject.parseObject(messageDTO.getCommonData());
            String traceId = commonData.getString("trace_id");
            messageMap.computeIfAbsent(traceId, k -> new ArrayList<>()).add(messageDTO);
        }
        JSONObject commonData = JSONObject.parseObject(message.getCommonData());
        String traceId = commonData.getString("trace_id");
        Map<String, String> messageIdApplicationIdMap = new HashMap<>();
        if (StringUtils.isNotBlank(traceId)) {
            messageIdApplicationIdMap = messageMap.get(traceId).stream().collect(Collectors.toMap(AidaMessagesPo::getId, AidaMessagesPo::getAppId));
        }
        messageIdApplicationIdMap.put(message.getId(), message.getAppId());
        List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList = new ArrayList<>();
        List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> aidaTraceList = new ArrayList<>();
        List<WorkflowLogResult> workflowLogResultList = new ArrayList<>();
        for (Map.Entry<String, String> entry : messageIdApplicationIdMap.entrySet()) {
            WorkflowLogResult workflowLogResult = new WorkflowLogResult();
            workflowLogResult.setWorkflowNodeExeDetailListFuture(WORKFLOW_LOG_QUERY_POOL.submit(() -> aidaInvokeServiceProxy.getWorkFlowNodeLogList(entry.getKey())));
            workflowLogResult.setAppId(entry.getValue());
            workflowLogResult.setMessageId(entry.getKey());
            workflowLogResultList.add(workflowLogResult);
        }
        for (WorkflowLogResult workflowLogResult : workflowLogResultList) {
            try {
                List<WorkflowNodeExeDetailsDTO> curMessageLogList = workflowLogResult.getWorkflowNodeExeDetailListFuture().get();
                if (CollectionUtils.isNotEmpty(curMessageLogList)) {
                    logList.addAll(buildAidaTraceList(curMessageLogList, workflowLogResult.getAppId()));
                }
            } catch (Exception e) {
                log.error("getNodeExeDetailsList error,appId={},messageId={}", workflowLogResult.getAppId(), workflowLogResult.getMessageId(), e);
            }
        }
        logList.sort(Comparator.comparing(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getStartTime));
        Optional.ofNullable(logList).ifPresent(aidaTraceList::addAll);
        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> nodeMap = new HashMap<>();
        nodeMap.put("logList", logList);
        nodeMap.put("aidaTraceList", aidaTraceList);
        return nodeMap;
    }

    /***
     * 获取大模型节点的日志信息
     * key 为大模型消息id
     * value 为大模型节点的trace链路日志信息
     * @param messageList
     * @param llmMessageIdList
     * @return
     */
    private Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> getAidaTraceMap(List<AidaMessagesPo> messageList, List<String> llmMessageIdList) {

        // 按照traceId分组
        Map<String, List<AidaMessagesPo>> messageMap = messageList.stream()
                .filter(aidaMessagesPo -> StringUtils.isNotBlank(aidaMessagesPo.getCommonData()))
                .collect(Collectors.groupingBy(InspectWorkbenchExecuteServiceImpl::getMessageTraceId, Collectors.toList()));

        // 过滤出大模型消息  key 为大模型消息id value 为大模型节点的trace消息
        List<Pair<String, AidaMessagesPo>> messageAllTraceMap = messageList.stream()
                .filter(aidaMessagesPo -> llmMessageIdList.contains(aidaMessagesPo.getId()))
                .flatMap(aidaMessagesPo -> {
                    List<AidaMessagesPo> aidaMessagesPoList = messageMap.getOrDefault(getMessageTraceId(aidaMessagesPo), Lists.newArrayList());
                    return aidaMessagesPoList.stream().map(aidaMessages -> Pair.of(aidaMessagesPo.getId(), aidaMessages));
                })
                .collect(Collectors.toList());

        // 并行处理
        List<CompletableFuture<Pair<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>>>> futures = messageAllTraceMap.stream()
                .map(pair -> CompletableFuture.supplyAsync(() -> Pair.of(pair.getKey(), getNodeExeDetailsList(pair.getValue().getAppId(), pair.getValue().getId())), AIDA_SUB_APPLICATION_POOL))
                .collect(Collectors.toList());

        // 等待所有的CompletableFuture完成并收集结果
        Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> aidaTraceMap = futures.stream()
                .map(CompletableFuture::join)  // 阻塞直到每个任务完成
                .flatMap(pair -> pair.getValue().stream().map(aidaTrace -> Pair.of(pair.getKey(), aidaTrace)))
                .sorted(Comparator.comparing(pair -> pair.getValue().getStartTime()))
                .collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue, Collectors.toList())));
        return aidaTraceMap;
    }

    private static String getMessageTraceId(AidaMessagesPo aidaMessagesPo) {
        JSONObject commonData = JSONObject.parseObject(aidaMessagesPo.getCommonData());
        return commonData.getString("trace_id");
    }

    private List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> getNodeExeDetailsList(String appId, String messageId) {
        // 查询大模型节点的日志信息
        List<WorkflowNodeExeDetailsDTO> curMessageLogList = aidaInvokeServiceProxy.getWorkFlowNodeLogList(messageId);
        return buildAidaTraceList(curMessageLogList, appId).stream()
                .sorted(Comparator.comparing(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getStartTime))
                .collect(Collectors.toList());
    }

    /**
     * 根据主流程appId与conversationId列表获取对应的messageDTO列表
     *
     * @param conversationIds 会话ID列表
     * @return 返回对应的messageDTO列表
     */
//    @Override
//    public List<MessagesDTO> listMessageByConvIdsAndMainAppId(List<String> conversationIds) {
//        log.info("[大表治理-复杂改造] 根据主流程appId与conversationId列表获取对应的message列表, conversationIds={}", conversationIds);
//        if (CollectionUtils.isEmpty(conversationIds)) {
//            return Lists.newArrayList();
//        }
//
//        try {
//            // 根据conversationIds到aidaMessages中查询所有appIds
//            List<String> aidaAppIdList = aidaMessagesGeneratorService.listAidaApplicationIdListByConversationIds(conversationIds);
//
//            return aidaInvokeServiceProxy.listByConvIdsAndAppIds(conversationIds, aidaAppIdList);
//        } catch (Exception e) {
//            log.error("[大表治理-复杂改造] listMessageByConvIdsAndMainAppId出现异常, conversationIds={}", conversationIds, e);
//            // 使用老链路进行兜底，确保线上运行稳定
//            return aidaInvokeServiceProxy.listByConversationIds(conversationIds);
//        }
//    }

    /**
     * 根据主流程appId通过Lion配置获取相对应的所有子流程appId
     *
     * @param mainAppId 主流程appId
     * @return 所有子流程appId列表
     */
    private List<String> getSubAppIdList(String mainAppId) {
        log.info("[大表治理-复杂改造] 根据主流程appId获取相对应的所有子流程appId, 主流程appId={}", mainAppId);
        String subApplication = Lion.getString(ConfigUtil.getAppkey(), LionConstants.SUB_APPLICATION_CONFIG);
        if (StringUtils.isBlank(subApplication)) {
            log.info("[大表治理-复杂改造] 根据主流程appId获取相对应的所有子流程appId, 主流程appId={}, 配置为空", mainAppId);
            return Collections.emptyList();
        }

        try {
            Map<String, List<String>> subApplicationMap = JSONObject.parseObject(subApplication, new TypeReference<Map<String, List<String>>>() {
            });
            if (MapUtils.isNotEmpty(subApplicationMap) && subApplicationMap.containsKey(mainAppId)) {
                return subApplicationMap.get(mainAppId);
            }
        } catch (Exception e) {
            log.error("[大表治理-复杂改造] 根据主流程appId获取相对应的所有子流程appId异常, 主流程appId={}", mainAppId, e);
        }

        return Collections.emptyList();
    }

    /**
     * 分页查询会话
     *
     * @param condition 查询条件
     * @return 会话列表
     */
//    private List<InspectWorkbenchDTO> pageSessionResult(PageParam<InspectWorkbenchConditionParam> condition) {
//        List<WorkspaceAppDTO> workspaceApp = getQueryWorkspaceApp(condition);
//        String applicationId = getSessionApplication(condition);
//        Future<List<String>> future = null;
//        for (WorkspaceAppDTO wp : workspaceApp) {
//            if (StringUtils.isNotBlank(applicationId)) {
//                List<String> applicationIdList = wp.getApplicationList().stream().map(WorkspaceAppDTO.Application::getApplicationId).collect(Collectors.toList());
//                if (!applicationIdList.contains(applicationId)) {
//                    continue;
//                }
//                future = AIDA_SUB_APPLICATION_POOL.submit(() -> getSubApplicationConversationId(condition.getCondition().getSessionId(), wp.getWorkspaceId(), applicationId, condition.getCondition().getStartTime(), condition.getCondition().getEndTime()));
//            }
//            PageParam<InspectWorkbenchConditionParam> curParam = new PageParam<>();
//            curParam.setPageNum(condition.getPageNum());
//            curParam.setPageSize(condition.getPageSize());
//            InspectWorkbenchConditionParam curCondition = new InspectWorkbenchConditionParam();
//            curCondition.setSessionId(condition.getCondition().getSessionId());
//            curCondition.setWorkspaceId(wp.getWorkspaceId());
//            curCondition.setStartTime(condition.getCondition().getStartTime());
//            curCondition.setEndTime(condition.getCondition().getEndTime());
//            curParam.setCondition(curCondition);
//            List<String> applicationIds;
//            if (StringUtils.isNotBlank(applicationId)) {
//                applicationIds = Collections.singletonList(applicationId);
//            } else {
//                applicationIds = wp.getApplicationList().stream().map(WorkspaceAppDTO.Application::getApplicationId).collect(Collectors.toList());
//            }
//            List<AidaSessionInfoDTO> aidaDataList = switchQueryStrategy(applicationIds, curParam);
//
//            if (CollectionUtils.isNotEmpty(aidaDataList)) {
//                // 二次校验，对比其他查询条件，如果不满足
//                if (!checkOtherCondition(condition.getCondition(), aidaDataList.get(0))) {
//                    return new ArrayList<>();
//                }
//                return convertAidaSession(aidaDataList, curCondition, future);
//            }
//        }
//        return new ArrayList<>();
//    }

    /**
     * 获取会话关联的应用
     *
     * @param condition 查询条件
     * @return 会话关联的应用ID
     */
//    private String getSessionApplication(PageParam<InspectWorkbenchConditionParam> condition) {
//        try {
//            if (StringUtils.isBlank(condition.getCondition().getSessionId())) {
//                return null;
//            }
//            List<AidaMessagesPo> aidaMessagesList = aidaMessagesGeneratorService.listBySessionId(condition.getCondition().getSessionId());
//            if (CollectionUtils.isEmpty(aidaMessagesList)) {
//                return null;
//            }
//            List<WorkspaceAppDTO> workspaceAppList = getWorkspaceAppInfo();
//            if (CollectionUtils.isEmpty(workspaceAppList)) {
//                return null;
//            }
//            List<String> applicationIdList = aidaMessagesList.stream().map(AidaMessagesPo::getAppId).distinct().collect(Collectors.toList());
//            Set<String> applicationIdSet = workspaceAppList.stream().flatMap(wp -> wp.getApplicationList().stream()).map(WorkspaceAppDTO.Application::getApplicationId).collect(Collectors.toSet());
//            for (String applicationId : applicationIdList) {
//                if (applicationIdSet.contains(applicationId)) {
//                    return applicationId;
//                }
//            }
//        } catch (Exception e) {
//            Cat.logError(e);
//            log.error("getSessionApplication error,condition={}", JSONObject.toJSONString(condition), e);
//        }
//
//        return null;
//    }

    /**
     * 校验其他查询条件
     *
     * @param condition      查询条件
     * @param sessionInfoDTO 会话信息
     * @return 是否满足条件
     */
    @SuppressWarnings("all")
    private boolean checkOtherCondition(InspectWorkbenchConditionParam condition, AidaSessionInfoDTO sessionInfoDTO) {
        // 订单ID不匹配，返回空
        if (StringUtils.isNotBlank(condition.getOrderId()) && !condition.getOrderId().equals(sessionInfoDTO.getOrderId())) {
            return false;
        }
        // 用户ID不匹配，返回空
        if (StringUtils.isNotBlank(condition.getUserId()) && !condition.getUserId().equals(sessionInfoDTO.getUserId())) {
            return false;
        }
        return true;
    }

    /**
     * 获取要查询空间应用列表
     *
     * @param pageCondition 查询条件
     * @return 空间应用列表
     */
//    private List<WorkspaceAppDTO> getQueryWorkspaceApp(PageParam<InspectWorkbenchConditionParam> pageCondition) {
//        List<WorkspaceAppDTO> collect;
//        InspectWorkbenchConditionParam condition = pageCondition.getCondition();
//        if (StringUtils.isNotBlank(condition.getApplicationId())) {
//            collect = Collections.singletonList(new WorkspaceAppDTO(condition.getWorkspaceId(), condition.getApplicationId()));
//        } else {
//            collect = getWorkspaceAppInfo();
//        }
//        return collect;
//    }

    /**
     * 构造AI搭执行链路
     *
     * @param logList 执行日志列表
     * @return AI搭执行链路
     */
    private List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> buildAidaTraceList(List<WorkflowNodeExeDetailsDTO> logList, String applicationId) {
        return logList.stream().map(log -> {
            InspectWorkbenchQueryDetailDTO.Detail.AidaTrace aidaTrace = new InspectWorkbenchQueryDetailDTO.Detail.AidaTrace();
            aidaTrace.setNodeId(log.getNodeId());
            aidaTrace.setNodeType(log.getNodeType());
            aidaTrace.setNodeName(log.getNodeName());
            aidaTrace.setLatency(log.getLatency());
            aidaTrace.setTokens(log.getTokens());
            aidaTrace.setInput(log.getInput());
            aidaTrace.setOutput(log.getOutput());
            aidaTrace.setProcessing(log.getProcessing());
            aidaTrace.setErrorMessage(log.getErrorMessage());
            aidaTrace.setApplicationId(applicationId);
            aidaTrace.setStartTime(log.getStartTime());
            aidaTrace.setEndTime(log.getEndTime());
            aidaTrace.setStatus(StringUtils.isBlank(log.getErrorMessage()) ? ApplicationResultEnum.SUCCESS.getCode() : ApplicationResultEnum.FAILED.getCode());
            return aidaTrace;
        }).collect(Collectors.toList());
    }

    private MessagesDTO getMatchMessage(List<MessagesDTO> messagesList, InspectWorkbenchDetailParam param) {
        if (CollectionUtils.isEmpty(messagesList)) {
            return null;
        }
        String originMessage = param.getOriginMessage();
        MessageDTO messageDTO = JSONObject.parseObject(originMessage, MessageDTO.class);
        JSONObject jsonObject = JSONObject.parseObject(messageDTO.getData());
        JSONObject extendInfo = jsonObject.getJSONObject("extendInfo");
        long timestamp = extendInfo.containsKey("triggerTime") ? extendInfo.getLongValue("triggerTime") : messageDTO.getAddTime().getTime();
        String answer = jsonObject.containsKey("answer") ? jsonObject.getString("answer") : jsonObject.getString("content");
        // 优先取时间最接近且回复内容相等的回复
        MessagesDTO matchMessage = messagesList.stream()
                .filter(message -> message.getAppId().equals(param.getApplicationId()))
                .filter(message -> StringUtils.isNotBlank(message.getAnswer()) && message.getAnswer().contains(answer))
                .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt() - timestamp)))
                .orElse(null);
        // 如果回复内容没有相等的，取时间最接近且小于等于当前时间的
        if (matchMessage == null) {
            matchMessage = messagesList.stream()
                    .filter(message -> message.getCreatedAt() <= timestamp)
                    .min(Comparator.comparingLong(message -> Math.abs(message.getCreatedAt() - timestamp)))
                    .orElse(null);
        }
        return matchMessage;
    }

    /**
     * 获取pb详情
     *
     * @param param 查询参数
     * @return 详情信息
     */
    private InspectWorkbenchQueryDetailDTO getPbDetail(InspectWorkbenchDetailParam param) {
        String originMessage = param.getOriginMessage();
        MessageDTO messageDTO = JSONObject.parseObject(originMessage, MessageDTO.class);
        JSONObject jsonObject = JSONObject.parseObject(messageDTO.getData());
        JSONObject extendInfo = jsonObject.getJSONObject("extendInfo");
        Long timestamp = extendInfo.containsKey("triggerTime") ? extendInfo.getLongValue("triggerTime") : messageDTO.getAddTime().getTime();
        String message = jsonObject.containsKey("answer") ? jsonObject.getString("answer") : jsonObject.getString("content");
        InspectWorkbenchTraceDTO inspectWorkbenchTrace = pbServiceProxy.getTraceInfo(param.getSessionId(), message, timestamp);
        CommonUtils.checkEval(inspectWorkbenchTrace != null, "未获取到会话trace记录，请稍后重试");
        InspectWorkbenchQueryDetailDTO inspectWorkbenchQueryDetail = new InspectWorkbenchQueryDetailDTO();
        inspectWorkbenchQueryDetail.setId(inspectWorkbenchTrace.getMessageId());
        inspectWorkbenchQueryDetail.setSignalList(buildSignalList(param, inspectWorkbenchTrace));
        inspectWorkbenchQueryDetail.setDetail(buildDetailInfo(inspectWorkbenchTrace));
        return inspectWorkbenchQueryDetail;
    }

    private UserInfo getAidaUserInfo(List<String> llmSessionIdList) {
        if (CollectionUtils.isNotEmpty(llmSessionIdList)) {
            List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByConversationIds(llmSessionIdList);
            List<AidaMessagesPo> firstLayerAidaMessagesPoList = operationAnalysisHelper.getFirstLayerAidaMessagesList(aidaMessagesPoList);
            if (CollectionUtils.isNotEmpty(firstLayerAidaMessagesPoList)) {
                return getUserInfoByAidaMessage(firstLayerAidaMessagesPoList.get(0));
            }
            log.warn(String.format("根据aida llmSessionIdList:%s获取aida用户信息失败, 获取第一层消息列表为空列表", llmSessionIdList));
        }
        return new UserInfo();
    }

    /**
     * 获取用户信息
     *
     * @param messagesList 消息列表
     * @return 用户信息
     */
    private UserInfo getUserInfoByAidaMessage(List<MessagesDTO> messagesList) {
        MessagesDTO message = messagesList.get(0);
        UserInfo userInfo = new UserInfo();
        if (StringUtils.isNotBlank(message.getExtraInfo())) {
            JSONObject jsonObject = JSONObject.parseObject(message.getExtraInfo());
            if (jsonObject.containsKey("userId")) {
                userInfo.setUserId(jsonObject.getString("userId"));
            }
            if (jsonObject.containsKey("userType")) {
                userInfo.setUserType(jsonObject.getString("userType"));
            }
        }
        return userInfo;
    }

    private UserInfo getUserInfoByAidaMessages(List<AidaMessagesPo> messagesList) {
        AidaMessagesPo message = messagesList.get(0);
        UserInfo userInfo = new UserInfo();
        if (StringUtils.isNotBlank(message.getExtraInfo())) {
            JSONObject jsonObject = JSONObject.parseObject(message.getExtraInfo());
            if (jsonObject.containsKey("userId")) {
                userInfo.setUserId(jsonObject.getString("userId"));
            }
            if (jsonObject.containsKey("userType")) {
                userInfo.setUserType(jsonObject.getString("userType"));
            }
        }
        return userInfo;
    }

    /**
     * 根据aida消息获取用户信息
     *
     * @param aidaMessagesPo aida消息
     * @return 用户信息
     */
    private UserInfo getUserInfoByAidaMessage(AidaMessagesPo aidaMessagesPo) {
        UserInfo userInfo = new UserInfo();
        if (null == aidaMessagesPo) {
            return userInfo;
        }
        userInfo.setUserId(aidaMessagesPo.getUserId());
        userInfo.setUserType(aidaMessagesPo.getUserType());
        return userInfo;
    }

    private void saveWorkbenchMistake(InspectWorkbenchCollectParam param) {
        CommonUtils.checkEval(PlatformTypeEnum.PB.getCode() == param.getPlatformType(), "目前只支持添加PB平台的数据到错题集");

        // 获取场景名称
        SceneInfoDTO sceneInfoDTO = pbServiceProxy.getSceneInfoBySessionId(param.getSessionId());
        if (sceneInfoDTO == null) {
            throw new EvalException("未获取到场景信息");
        }
        String mistakeDataSetName = String.format("回归评测-错题集-%s", sceneInfoDTO.getSceneName());

        // 通过场景获取空间
        String workspaceId = getWorkspaceIdByScene(sceneInfoDTO);

        // 获取会话详情数据
        InspectWorkbenchTraceDTO traceInfo = pbServiceProxy.getTraceInfo(param.getLlmMessageId());
        CommonUtils.checkEval(traceInfo != null, "未拉取到当前消息");

        // 获取信号信息
        List<InspectWorkbenchQueryDetailDTO.Signal> signalList = buildSignalList(traceInfo);
        Map<String, String> signalMap = signalList.stream().collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Signal::getSignalName, InspectWorkbenchQueryDetailDTO.Signal::getSignalValue, (a, b) -> a));

        // 获取Detail
        InspectWorkbenchQueryDetailDTO.Detail detail = buildDetailInfo(traceInfo);

        // 根据场景ID获取应用
        ApplicationConfigPo applicationConfigPo = getApplication(sceneInfoDTO, workspaceId);

        DatasetConditionParam datasetConditionParam = new DatasetConditionParam();
        datasetConditionParam.setApplicationList(applicationConfigPo.getId().toString());

        //  查询已有的数据集，根据场景ID和信号信息进行查询
        List<EvalDatasetPo> existingDatasets = evalDatasetGeneratorService.getByCondition(datasetConditionParam);
        //  检查已有的数据集是否与当前消息对应的信号信息一致，如果一致则保存错题集详情信息并返回
        if (CollectionUtils.isNotEmpty(existingDatasets) && checkSignalList(existingDatasets.get(0), signalMap)) {
            validateMessageNotInDataset(param, existingDatasets.get(0)); // 检查是否重复
            saveMistakeDatasetDetail(existingDatasets.get(0), param, detail, signalMap);
            return;
        }

        //  需要新增或更新的情况, 加锁后查询主库, 避免出现数据集并发更新, 并发问题出现概率低
        log.debug("需要更新或新增错题集");
        boolean lockAcquired = false;
        try {
            getQueryCollectLock(sceneInfoDTO.getSceneId()); // 加锁后查询，避免出现数据集并发更新
            lockAcquired = true;

            // 事务内会走主库
            List<EvalDatasetPo> dataset = evalDatasetGeneratorService.getByCondition(datasetConditionParam);
            EvalDatasetPo evalDataset;

            // 错题集不存在，构造错题集
            if (CollectionUtils.isEmpty(dataset)) {
                evalDataset = buildAndSaveEvalDataset(sceneInfoDTO, mistakeDataSetName, workspaceId, signalMap, applicationConfigPo);
            } else {
                evalDataset = dataset.get(0);
                validateMessageNotInDataset(param, evalDataset); // 检查是否重复
                checkSignalListAndUpdate(evalDataset, signalMap); // 检查是信号列是否存在新增
            }
            releaseQueryCollectLock(sceneInfoDTO.getSceneId()); // 错题集更新完成, 释放锁
            lockAcquired = false;
            // 保存错题集详情信息
            saveMistakeDatasetDetail(evalDataset, param, detail, signalMap);
        } finally {
            if (lockAcquired) {
                releaseQueryCollectLock(sceneInfoDTO.getSceneId());
            }
        }

    }

    /**
     * 判断是否查询PB
     *
     * @param condition 查询条件
     * @return 是否查询PB
     */
    private boolean isQueryPb(InspectWorkbenchConditionParam condition) {
        // 选择了空间和应用，只查询AI搭
        return !StringUtils.isNotBlank(condition.getWorkspaceId()) && !StringUtils.isNotBlank(condition.getApplicationId());
    }

    /**
     * 校验消息是否已经加入错题集
     *
     * @param param       请求参数
     * @param evalDataset 错题集对象
     */
    private void validateMessageNotInDataset(InspectWorkbenchCollectParam param, EvalDatasetPo evalDataset) {
        DatasetDetailConditionParam datasetDetailConditionParam = new DatasetDetailConditionParam();
        datasetDetailConditionParam.setDatasetId(evalDataset.getId());
        datasetDetailConditionParam.setLlmMessageId(param.getLlmMessageId());
        List<EvalDatasetDetailPo> evalDatasetDetailPoList = evalDatasetDetailGeneratorService.getByCondition(datasetDetailConditionParam);
        CommonUtils.checkEval(CollectionUtils.isEmpty(evalDatasetDetailPoList), "该消息已被加入错题集，请不要重复操作");
    }


    private EvalDatasetPo buildAndSaveEvalDataset(SceneInfoDTO sceneInfoDTO, String mistakeDataSetName, String workspaceId, Map<String, String> signalMap, ApplicationConfigPo applicationConfigPo) {
        EvalDatasetPo evalDataset;
        evalDataset = new EvalDatasetPo();
        evalDataset.setName(mistakeDataSetName);
        evalDataset.setApplicationList(applicationConfigPo.getId().toString());

        //  设置数据集创建方式为日志转存, 平台来源SYSTEM
        evalDataset.setCreateMethod(DatasetCreateionMethodEnum.LOG_TRANS.getCode());
        evalDataset.setPlatformType(PlatformTypeEnum.SYSTEM.getCode());
        evalDataset.setType(DatasetTypeEnum.REGRESSION_DATASET.getCode());

        //  设置数据集创建者和更新者 设置更新时间
        evalDataset.setCreatorMis(UserUtils.getUser().getLogin());
        evalDataset.setUpdaterMis(UserUtils.getUser().getLogin());
        Date date = new Date();
        evalDataset.setGmtModified(date);
        evalDataset.setGmtCreated(date);

        // 设置数据集场景和平台应用
        evalDataset.setPlatformApp(String.valueOf(sceneInfoDTO.getSceneId()));
        evalDataset.setApplicationList(applicationConfigPo.getId().toString());
        evalDataset.setPlatformWorkspace(workspaceId);

        //  设置数据集扩展参数
        evalDataset.setExtra(JSONObject.toJSONString(getDatasetExtraParam(signalMap)));

        evalDatasetGeneratorService.save(evalDataset);
        return evalDataset;
    }

    private ApplicationConfigPo getApplication(SceneInfoDTO sceneInfoDTO, String workspaceId) {
        ApplicationParam applicationParam = new ApplicationParam();
        applicationParam.setName(sceneInfoDTO.getSceneName());
        applicationParam.setSource(ApplicationSourceEnum.VIRTUAL.getCode());
        applicationParam.setPbSceneId(sceneInfoDTO.getSceneId());
        applicationParam.setCreateMis(UserUtils.getUser().getLogin());
        AidaModelConfig aidaModelConfig = new AidaModelConfig();
        aidaModelConfig.setWorkspaceId(workspaceId);
        applicationParam.setAidaModelConfig(aidaModelConfig);
        return applicationExecuteService.getOrCreateFromThirdSystem(applicationParam);
    }

    /**
     * 检查信号列是否存在新增
     *
     * @param evalDataset 数据集对象
     * @param signalMap   信号
     * @return true-不存在新增信号列 false-存在新增信号列
     */
    private boolean checkSignalList(EvalDatasetPo evalDataset, Map<String, String> signalMap) {
        DatasetExtraParam extra = JSONObject.parseObject(evalDataset.getExtra(), DatasetExtraParam.class);
        // 获取Head List列的集合
        Set<String> headSet = new HashSet<>(extra.getHeadList());
        // 检查是否存在新增信号列
        int signalCount = headSet.size();
        headSet.addAll(signalMap.keySet());
        return signalCount == headSet.size();
    }

    /**
     * 检查信号列是否存在新增
     * 存在新增则更新数据集表头
     *
     * @param evalDataset 数据集对象
     * @param signalMap   信号映射表
     */
    private void checkSignalListAndUpdate(EvalDatasetPo evalDataset, Map<String, String> signalMap) {
        DatasetExtraParam extra = JSONObject.parseObject(evalDataset.getExtra(), DatasetExtraParam.class);
        // 获取Head List列的集合，并保证顺序
        Set<String> headSet = new LinkedHashSet<>(extra.getHeadList());
        // 检查是否存在新增信号列
        int signalCount = headSet.size();
        headSet.addAll(signalMap.keySet());
        // 不存在新增信号列
        if (signalCount == headSet.size()) {
            return;
        }
        // 设置新增后 HeadList 和 MapList
        extra.setHeadList(new ArrayList<>(headSet));
        List<SingleTemplateFieldBindDTO> newMapList = headSet.stream().map(head -> new SingleTemplateFieldBindDTO(head, DataTypeEnum.APPLICATION_VARIABLE)).collect(Collectors.toList());
        extra.setMapList(newMapList);
        // 更新数据集扩展参数
        evalDataset.setExtra(JSONObject.toJSONString(extra));
        evalDataset.setUpdaterMis(UserUtils.getUser().getLogin());
        evalDataset.setGmtModified(new Date());
        evalDatasetGeneratorService.updateById(evalDataset);
    }

    private void getQueryCollectLock(Long sceneId) {
        int expireTime = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_QUERY_COLLECT_LOCK_EXPIRE_TIME, 30);
        int waitTime = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_QUERY_COLLECT_LOCK_WAIT_TIME, 5000);
        boolean lockAcquired = false;
        long startTime = System.currentTimeMillis();
        try {
            while (!lockAcquired && System.currentTimeMillis() - startTime < waitTime) {
                lockAcquired = redisClientProxy.incrWithUpperBound(new StoreKey(RedisConstants.WORKBENCH_QUERY_COLLECT_LOCK, sceneId), 1, 1, 0, expireTime).isOperated();
                if (!lockAcquired) {
                    Thread.sleep(100);
                }
            }
        } catch (Exception e) {
            log.error("获取错题集锁失败", e);
        }
        CommonUtils.checkEval(lockAcquired, "添加频繁请稍后尝试");
    }

    private void releaseQueryCollectLock(Long sceneId) {
        try {
            redisClientProxy.delete(new StoreKey(RedisConstants.WORKBENCH_QUERY_COLLECT_LOCK, sceneId));
        } catch (Exception e) {
            log.error("释放错题集锁失败", e);
        }
    }

    /**
     * 通过PB场景获取工作空间ID
     * <P>没有对应的空间返回 null</P>
     *
     * @param sceneInfoDTO 场景信息
     * @return 工作空间ID
     */
    private String getWorkspaceIdByScene(SceneInfoDTO sceneInfoDTO) {
        if (sceneInfoDTO == null || sceneInfoDTO.getSceneName() == null) {
            throw new EvalException("场景信息不完整");
        }

        // 获取场景与工作空间映射关系
        List<SceneRelation> list = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SCENE_WORKSPACE_RELATION, SceneRelation.class);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, SceneRelation> sceneRelationMap = list.stream().collect(Collectors.toMap(SceneRelation::getSceneTypeName, Function.identity()));
            for (String sceneTypeName : sceneRelationMap.keySet()) {
                for (List<String> keywords : sceneRelationMap.get(sceneTypeName).getKeywordsList()) {
                    if (keywords.stream().allMatch(keyword -> sceneInfoDTO.getSceneName().contains(keyword))) {
                        return sceneRelationMap.get(sceneTypeName).getPlatformWorkspace();
                    }
                }
            }
        }
        return null; // 场景未找到对应的工作空间
    }

    /**
     * 构造请求详情参数
     *
     * @param request 请求参数
     * @param dto     会话详情数据传输对象
     * @return 返回请求详情参数
     */
    private InspectWorkbenchDetailParam getInspectWorkbenchDetailParam(AutoInspectionRequestParam request, InspectWorkbenchSessionDetailDTO dto) {
        InspectWorkbenchDetailParam inspectWorkbenchDetailParam = new InspectWorkbenchDetailParam();
        inspectWorkbenchDetailParam.setSessionId(request.getSessionId());
        inspectWorkbenchDetailParam.setMessageId(dto.getMessageId());
        inspectWorkbenchDetailParam.setOriginMessage(dto.getOriginMessage());
        inspectWorkbenchDetailParam.setPlatform(request.getPlatformType());
        inspectWorkbenchDetailParam.setLlmSessionIdList(request.getLlmSessionId());
        inspectWorkbenchDetailParam.setWorkspaceId(request.getWorkspaceId());
        inspectWorkbenchDetailParam.setApplicationId(request.getApplicationId());
        return inspectWorkbenchDetailParam;
    }

    /**
     * 移除AIDA数据中机器质检不需要的字段
     *
     * @param queryDetailDTO 查询详情数据传输对象
     */
    private void removeAidaUnusedData(InspectWorkbenchQueryDetailDTO queryDetailDTO) {
        queryDetailDTO.setSignal(null);
        queryDetailDTO.setSignalList(null);
        queryDetailDTO.getDetail().setTraceList(null);
        queryDetailDTO.getDetail().setAidaTraceList(null);
    }

    private void saveMistakeDatasetDetail(EvalDatasetPo datasetPo, InspectWorkbenchCollectParam param, InspectWorkbenchQueryDetailDTO.Detail detail, Map<String, String> signalMap) {
        // 获取相关数据
        // 获取User Id Order Id
        DialogSessionConditionParam condition = new DialogSessionConditionParam();
        condition.setSessionId(param.getSessionId());
        PageResultDTO<DialogSessionInfoDTO> pageData = pbServiceProxy.pageDialogSession(condition, 1, 10);
        DialogSessionInfoDTO sessionInfo = null;
        if (pageData == null || CollectionUtils.isEmpty(pageData.getData())) {
            log.warn("未获取到会话信息: {}", condition);
        } else {
            sessionInfo = pageData.getData().get(0);
        }

        // 质检状态
        String feedback = null;
        String feedbackMis = null;
        List<InspectWorkbenchEvalPo> inspectWorkbenchEvalList = inspectWorkbenchEvalGeneratorService.getBySessionIdAndMessageId(param.getSessionId(), param.getMessageId());
        if (CollectionUtils.isNotEmpty(inspectWorkbenchEvalList)) {
            InspectWorkbenchEvalPo po = inspectWorkbenchEvalList.get(0);
            InspectAgreeStatusEnum status = InspectAgreeStatusEnum.getByCode(po.getAgreeStatus());
            feedback = status == null ? null : status.getDescription();
            feedbackMis = po.getEvalMis();
        }


        // 获取输入输出
        String input = null;
        String output = null;
        for (InspectWorkbenchQueryDetailDTO.Detail.BasicInfo basicInfo : detail.getBasicInfo()) {
            if ("输入".equals(basicInfo.getDisplayName())) {
                input = basicInfo.getDisplayContent();
            } else if ("输出".equals(basicInfo.getDisplayName())) {
                output = basicInfo.getDisplayContent();
            }
        }

        // 获取历史消息
        List<DialogChatItem> history = pbServiceProxy.getHistoryBySessionId(param.getSessionId());
        history.sort(Comparator.comparing(DialogChatItem::getId));
        List<Map<String, String>> historyList = new ArrayList<>();

        for (DialogChatItem item : history) {
            //  判断当前消息详情是否与会话详情相同，如果相同则跳出循环
            if (StringUtils.equals(String.valueOf(item.getId()), param.getLlmMessageId())) {
                break;
            }
            //  过滤掉非用户和助手的消息，只保留用户和助手的消息
            if (StringUtils.equals(item.getRole(), "user") || StringUtils.equals(item.getRole(), "assistant")) {
                HashMap<String, String> map = new HashMap<>();
                map.put("role", item.getRole());
                map.put("message", item.getMessage());
                historyList.add(map);
            }
        }


        // 构造数据集详情
        EvalDatasetDetailPo detailPo = new EvalDatasetDetailPo();

        HashMap<String, String> map = new HashMap<>();
        map.put(WorkbenchTemplateFieldBindEnum.SESSION_ID.getInfo(), param.getSessionId());
        map.put(WorkbenchTemplateFieldBindEnum.MESSAGE_ID.getInfo(), param.getMessageId());
        map.put(WorkbenchTemplateFieldBindEnum.USER_ID.getInfo(), sessionInfo == null ? null : sessionInfo.getUserId());
        map.put(WorkbenchTemplateFieldBindEnum.ORDER_ID.getInfo(), sessionInfo == null ? null : sessionInfo.getOrderId());
        map.put(WorkbenchTemplateFieldBindEnum.FEEDBACK.getInfo(), feedback);
        map.put(WorkbenchTemplateFieldBindEnum.FEEDBACK_MIS.getInfo(), feedbackMis);
        map.put(WorkbenchTemplateFieldBindEnum.HISTORY.getInfo(), JSONArray.toJSONString(historyList));
        map.put(WorkbenchTemplateFieldBindEnum.INPUT.getInfo(), input);
        map.put(WorkbenchTemplateFieldBindEnum.OUTPUT.getInfo(), output);
        map.putAll(signalMap);

        detailPo.setDatasetId(datasetPo.getId());
        detailPo.setContent(JSONArray.toJSONString(map));
        detailPo.setSessionId(param.getSessionId());
        detailPo.setLlmMessageId(param.getLlmMessageId());
        detailPo.setCreatorMis(UserUtils.getUser().getLogin());
        detailPo.setUpdaterMis(UserUtils.getUser().getLogin());
        Date date = new Date();
        detailPo.setGmtModified(date);
        detailPo.setGmtCreated(date);
        evalDatasetDetailGeneratorService.save(detailPo);
    }

    private DatasetExtraParam getDatasetExtraParam(Map<String, String> signalMap) {
        // 构造 Extra 数据
        DatasetExtraParam extraParam = new DatasetExtraParam();
        // 构造 head
        List<String> headList = new ArrayList<>();
        headList.addAll(WorkbenchTemplateFieldBindEnum.getAllInfo());
        headList.addAll(signalMap.keySet());

        // 构造 map
        List<SingleTemplateFieldBindDTO> mapList = new ArrayList<>(WorkbenchTemplateFieldBindEnum.getAllFieldBinds());
        for (String signalName : signalMap.keySet()) {
            SingleTemplateFieldBindDTO singleTemplateFieldBindDTO = new SingleTemplateFieldBindDTO();
            singleTemplateFieldBindDTO.setColumnName(signalName);
            singleTemplateFieldBindDTO.setColumnType(DataTypeEnum.APPLICATION_VARIABLE.getCode());
            mapList.add(singleTemplateFieldBindDTO);
        }

        extraParam.setHeadList(headList);
        extraParam.setMapList(mapList);
        return extraParam;
    }

    /**
     * 获取指标映射关系
     *
     * @return 指标映射关系，键为自动指标ID，值为手动指标ID
     */
    private Map<Long, Long> getMetricMapping() {
        List<MetricsRelation> list = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SESSION_METRIC_RELATION, MetricsRelation.class);
        return list.stream().flatMap(metricsRelation -> metricsRelation.getMetricsRelation().stream())
                .collect(Collectors.toMap(WorkbenchMetricsMappingDTO::getAutoMetricId, WorkbenchMetricsMappingDTO::getManualMetricId, (a, b) -> a));
    }

    /**
     * 保存会话信息
     *
     * @param sessionId    会话ID
     * @param platformType 平台类型
     * @param llmSessionId LLM会话ID列表
     * @param workspaceApp 工作空间应用信息
     * @param status       质检状态
     */
    @Deprecated
    private void saveSessionInfo(String sessionId, Integer platformType, List<String> llmSessionId, WorkspaceAppDTO workspaceApp, YesNoEnum status) {
        WorkbenchSessionInfoPo info = workbenchSessionInfoGeneratorService.getBySessionId(sessionId);
        if (info != null) {
            log.info("Session info 已经存在 sessionId: {}", sessionId);
            return;
        }

        log.info("添加新Session Info sessionId: {}", sessionId);
        WorkbenchSessionInfoPo inspectWorkbenchDTO = null;
        if (PlatformTypeEnum.AI.getCode() == platformType) {
//            inspectWorkbenchDTO = getAidaSessionInfo(sessionId, llmSessionId, workspaceApp, status);
        } else if (PlatformTypeEnum.PB.getCode() == platformType) {
//            inspectWorkbenchDTO = getPBSessionInfo(sessionId, status);
        }
        if (inspectWorkbenchDTO == null) {
            log.error("没有查询到对应Session信息 sessionId: {}", sessionId);
            throw new EvalException("没有查询到对应Session信息");
        }

        workbenchSessionInfoGeneratorService.save(inspectWorkbenchDTO);
    }


    private WorkbenchSessionInfoPo getAidaSessionInfo(String sessionId, List<String> conversionIdList, WorkspaceAppDTO workspaceApp, YesNoEnum status) {
        String appId = workspaceApp.getApplicationList().get(0).getApplicationId();
        AidaMessagesConditionParam conditionParam = new AidaMessagesConditionParam();
        conditionParam.setSessionId(sessionId);
        conditionParam.setApplicationId(appId);
        conditionParam.setConversationIds(conversionIdList);
        List<AidaMessagesPo> messagesDTOS = aidaMessagesGeneratorService.listByCondition(conditionParam);

        // 获取第一层数据
//        messagesDTOS = operationAnalysisHelper.getFirstLayerByMessageListFromOriginAida(messagesDTOS);

        // 填充信息
        WorkbenchSessionInfoPo workbenchSessionInfoPo = new WorkbenchSessionInfoPo();
        workbenchSessionInfoPo.setPlatformType(PlatformTypeEnum.AI.getCode());
        workbenchSessionInfoPo.setSessionId(sessionId);
        workbenchSessionInfoPo.setLlmSessionId(String.join(",", conversionIdList));
        workbenchSessionInfoPo.setSessionTurn(messagesDTOS.size());
        if (CollectionUtils.isNotEmpty(messagesDTOS)) {
            messagesDTOS.sort(Comparator.comparing(AidaMessagesPo::getCreatedAt));
            AidaMessagesPo messages = messagesDTOS.get(0);
            JSONObject parsed = DataConvertUtil.tryConvertJson(messages.getExtraInfo());
            if (parsed != null) {
                workbenchSessionInfoPo.setSessionUserId(parsed.getString("userId"));
                workbenchSessionInfoPo.setSessionOrderId(parsed.getString("orderId"));
                workbenchSessionInfoPo.setSessionGmtCreated(messages.getCreatedAt());
            }
        }
        workbenchSessionInfoPo.setStatus(status.getCode());
        workbenchSessionInfoPo.setPlatformWorkspace(workspaceApp.getWorkspaceId());
        workbenchSessionInfoPo.setPlatformApp(workspaceApp.getApplicationList().get(0).getApplicationId());

        // 添加创建人和日期
        setWorkbenchSessionInfoPoUserAndDate(workbenchSessionInfoPo);
        return workbenchSessionInfoPo;
    }

    /**
     * 获取手动指标ID列表
     *
     * @return 手动指标ID列表
     */
    private List<Long> getManualMetricId(String sessionId, String applicationId) {
        List<MetricsRelation> list = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_SESSION_METRIC_RELATION, MetricsRelation.class);
        Map<String, MetricsRelation> collect = list.stream().collect(Collectors.toMap(MetricsRelation::getSceneTypeName, Function.identity()));
        if (StringUtils.isBlank(applicationId)) {
            String sceneTypeName = getSceneTypeNameBySessionId(sessionId);
            return sceneTypeName != null && collect.containsKey(sceneTypeName) ? collect.get(sceneTypeName).getManulaMetricsIdList() : Collections.emptyList();
        } else {
            return collect.containsKey(applicationId) ? collect.get(applicationId).getManulaMetricsIdList() : Collections.emptyList();
        }
    }

    private WorkbenchSessionInfoPo getPBSessionInfo(String sessionId, YesNoEnum status) {
        // 获取数据
        DialogSessionConditionParam param = new DialogSessionConditionParam();
        param.setSessionId(sessionId);
        PageResultDTO<DialogSessionInfoDTO> pageData = pbServiceProxy.pageDialogSession(param, 1, 10);
        if (pageData == null || CollectionUtils.isEmpty(pageData.getData())) {
            return null;
        }
        DialogSessionInfoDTO dialogSessionInfoDTO = pageData.getData().get(0);

        // 填充信息
        WorkbenchSessionInfoPo workbenchSessionInfoPo = new WorkbenchSessionInfoPo();
        workbenchSessionInfoPo.setPlatformType(PlatformTypeEnum.PB.getCode());
        workbenchSessionInfoPo.setSessionId(dialogSessionInfoDTO.getSessionId());
        workbenchSessionInfoPo.setLlmSessionId(dialogSessionInfoDTO.getId());
        workbenchSessionInfoPo.setSessionGmtCreated(dialogSessionInfoDTO.getTime());
        workbenchSessionInfoPo.setSessionTurn(dialogSessionInfoDTO.getTurn());
        workbenchSessionInfoPo.setSessionUserId(dialogSessionInfoDTO.getUserId());
        workbenchSessionInfoPo.setSessionOrderId(dialogSessionInfoDTO.getOrderId());
        workbenchSessionInfoPo.setStatus(status.getCode());

        // 添加创建人和日期
        setWorkbenchSessionInfoPoUserAndDate(workbenchSessionInfoPo);
        return workbenchSessionInfoPo;
    }

    private void setWorkbenchSessionInfoPoUserAndDate(WorkbenchSessionInfoPo workbenchSessionInfoPo) {
        // 添加创建人
        workbenchSessionInfoPo.setCreatorMis(UserUtils.getUser().getLogin());
        workbenchSessionInfoPo.setUpdaterMis(UserUtils.getUser().getLogin());
        Date date = new Date();
        workbenchSessionInfoPo.setGmtModified(date);
        workbenchSessionInfoPo.setGmtCreated(date);
    }

    private void appendEvalStatus(PageData<InspectWorkbenchDTO> pageData) {
        if (pageData == null || CollectionUtils.isEmpty(pageData.getData())) {
            return;
        }
        // 补充点赞点踩和质检信息
        appendEvalStatus(pageData.getData());
    }

    /**
     * 验证并获取质检信息列表
     *
     * @param param 包含质检信息ID的参数对象
     * @return 验证后的质检信息列表
     * @throws CheckException 当没有查询到对应的机器质检结果或质检信息数量不匹配时抛出
     */
    private List<WorkbenchInspectInfoPo> validateAndFetchInspectInfoList(WorkbenchAdoptParam param) {
        // 根据传入的ID列表查询质检信息
        List<WorkbenchInspectInfoPo> infoList = new ArrayList<>(workbenchInspectInfoGeneratorService.listByIdsAndType(param.getInspectInfoId(), MetricEvalTypeEnum.AUTO.getCode()));

        // 检查是否查询到质检信息
        if (CollectionUtils.isEmpty(infoList)) {
            log.error("没有查询到对应的机器质检结果");
            throw new CheckException("没有查询到对应的机器质检结果");
        }

        // 验证查询到的质检信息数量是否与请求的ID数量一致
        validateInspectInfoCount(param.getInspectInfoId(), infoList);

        return infoList;
    }

    /**
     * 校验质检信息数量是否匹配
     *
     * @param requestedIds  请求的质检信息ID列表
     * @param foundInfoList 查询到的质检信息列表
     */
    private void validateInspectInfoCount(List<Long> requestedIds, List<WorkbenchInspectInfoPo> foundInfoList) {
        if (foundInfoList.size() != requestedIds.size()) {
            Set<Long> foundIds = foundInfoList.stream().map(WorkbenchInspectInfoPo::getId).collect(Collectors.toSet());
            Set<Long> missingIds = new HashSet<>(requestedIds);
            missingIds.removeAll(foundIds);
            Set<Long> extraIds = new HashSet<>(foundIds);
            requestedIds.forEach(extraIds::remove);
            log.error("质检信息数量不匹配。请求ID数: {}, 找到ID数: {}, 缺失ID: {}, 多余ID: {}",
                    requestedIds.size(), foundIds.size(), missingIds, extraIds);
            throw new CheckException("质检信息数量不匹配");
        }
    }

    /**
     * 处理质检信息
     *
     * @param info     质检信息
     * @param metricId 指标配置信息
     */
    private void processInspectInfo(WorkbenchInspectInfoPo info, Long metricId) {
        List<WorkbenchInspectInfoPo> manualInfoList = fetchManualInspectInfo(info, metricId);
        if (CollectionUtils.isEmpty(manualInfoList)) {
            saveWorkbenchInspectInfo(metricId, info);
        } else {
            updateManualInspectInfoFromAuto(manualInfoList.get(0), info);
        }
    }

    /**
     * 获取人工质检信息
     *
     * @param info     机器质检信息
     * @param metricId 指标ID
     * @return 人工质检信息列表
     */
    private List<WorkbenchInspectInfoPo> fetchManualInspectInfo(WorkbenchInspectInfoPo info, Long metricId) {
        return workbenchInspectInfoGeneratorService.getByCondition(WorkbenchInspectInfoConditionParam.builder()
                .sessionId(info.getSessionId())
                .messageId(info.getMessageId())
                .metricId(metricId)
                .type(MetricEvalTypeEnum.MANUAL.getCode())
                .dimension(info.getDimension())
                .creator(UserUtils.getUser().getLogin())
                .build());
    }

    /**
     * 保存质检信息
     *
     * @param result 质检结果
     * @param param  人工质检参数
     */
    private void saveWorkbenchInspectInfo(ManualInspectParam.InspectResult result, ManualInspectParam param) {
        WorkbenchInspectInfoPo inspectInfo = createWorkbenchInspectInfo(param, result);
        workbenchInspectInfoGeneratorService.save(inspectInfo);
    }

    /**
     * 保存被采纳的自动质检信息为人工质检信息
     *
     * @param manualMetricId 人工指标ID
     * @param autoInfo       自动质检信息
     */
    private void saveWorkbenchInspectInfo(Long manualMetricId, WorkbenchInspectInfoPo autoInfo) {
        WorkbenchInspectInfoPo manualInfo = convertAutoToManualInspectInfo(autoInfo, manualMetricId);
        log.info("保存被采纳的自动质检信息为人工质检信息, manualInfo:{}", manualInfo);
        workbenchInspectInfoGeneratorService.save(manualInfo);
    }

    /**
     * 将自动质检信息转换为人工质检信息
     *
     * @param autoInfo 自动质检信息
     * @param metricId 人工指标ID
     * @return 转换后的人工质检信息
     */
    private WorkbenchInspectInfoPo convertAutoToManualInspectInfo(WorkbenchInspectInfoPo autoInfo, Long metricId) {
        WorkbenchInspectInfoPo manualInfo = new WorkbenchInspectInfoPo();
        BeanUtils.copyProperties(autoInfo, manualInfo);
        manualInfo.setId(null);
        manualInfo.setMetricId(metricId);
        manualInfo.setType(MetricEvalTypeEnum.MANUAL.getCode());
        manualInfo.setAdopted(YesNoEnum.NO.getCode());
        setAuditInfo(manualInfo);
        return manualInfo;
    }

    /**
     * 根据机器质检信息更新人工质检信息
     *
     * @param manualInfo 人工质检信息
     * @param autoInfo   机器质检信息
     */
    private void updateManualInspectInfoFromAuto(WorkbenchInspectInfoPo manualInfo, WorkbenchInspectInfoPo autoInfo) {
        manualInfo.setMetricResult(autoInfo.getMetricResult());
        manualInfo.setUpdaterMis(UserUtils.getUser().getLogin());
        manualInfo.setGmtModified(new Date());
        log.info("更新人工质检信息, manualInfo:{}", manualInfo);
        workbenchInspectInfoGeneratorService.updateById(manualInfo);
    }

    /**
     * 更新质检信息为已采纳
     *
     * @param infoList 机器质检信息列表
     */
    private void updateAdoptedStatus(List<WorkbenchInspectInfoPo> infoList) {
        infoList.forEach(info -> {
            info.setAdopted(YesNoEnum.YES.getCode());
            info.setUpdaterMis(UserUtils.getUser().getLogin());
            info.setGmtModified(new Date());
        });
        log.info("采纳机器质检结果, ids:{}", infoList.stream().map(WorkbenchInspectInfoPo::getId).collect(Collectors.toList()));
        workbenchInspectInfoGeneratorService.updateBatchById(infoList);
    }

    private WorkbenchInspectInfoPo createWorkbenchInspectInfo(ManualInspectParam param, ManualInspectParam.InspectResult result) {
        WorkbenchInspectInfoPo inspectInfo = new WorkbenchInspectInfoPo();
        setBasicInfo(inspectInfo, param);
        setPlatformInfo(inspectInfo, param);
        setDimensionInfo(inspectInfo, param);
        setMetricInfo(inspectInfo, result);
        setAuditInfo(inspectInfo);
        return inspectInfo;
    }

    private void setBasicInfo(WorkbenchInspectInfoPo inspectInfo, WorkbenchCommonParam param) {
        inspectInfo.setSessionId(param.getSessionId());
        inspectInfo.setLlmSessionId(CollectionUtils.isNotEmpty(param.getLlmSessionId()) ? String.join(",", param.getLlmSessionId()) : null);
        inspectInfo.setPlatformType(param.getPlatformType());
    }

    private void setPlatformInfo(WorkbenchInspectInfoPo inspectInfo, WorkbenchCommonParam param) {
        if (param.getPlatformType() == PlatformTypeEnum.AI.getCode()) {
            inspectInfo.setPlatformWorkspace(param.getWorkspaceId());
            inspectInfo.setPlatformApp(param.getApplicationId());
        }
    }

    private void setDimensionInfo(WorkbenchInspectInfoPo inspectInfo, WorkbenchCommonParam param) {
        inspectInfo.setDimension(param.getDimension());
        if (param.getDimension() == DimensionTypeEnum.QUERY.getCode()) {
            inspectInfo.setMessageId(param.getMessageId());
            inspectInfo.setLlmMessageId(param.getLlmMessageId());
        }
    }

    private void setMetricInfo(WorkbenchInspectInfoPo inspectInfo, ManualInspectParam.InspectResult result) {
        inspectInfo.setMetricId(result.getMetricId());
        inspectInfo.setMetricResult(JSONArray.toJSONString(result.getResult()));
        inspectInfo.setNote(result.getResultNote());
        inspectInfo.setType(MetricEvalTypeEnum.MANUAL.getCode());
    }

    private void setAuditInfo(WorkbenchInspectInfoPo inspectInfo) {
        String currentUser = UserUtils.getUser().getLogin();
        Date currentTime = new Date();
        inspectInfo.setCreatorMis(currentUser);
        inspectInfo.setUpdaterMis(currentUser);
        inspectInfo.setGmtCreated(currentTime);
        inspectInfo.setGmtModified(currentTime);
    }


    @Override
    public List<AutoInspectionStatisticsDTO> getAutoInspectionStatistics() {
        return workbenchInspectInfoGeneratorService.getAutoInspectionStatistics();
    }

    private List<InspectWorkbenchQueryDetailDTO.AidaSignal> buildGroupSignal(InspectWorkbenchDetailParam param, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList, Map<String, String> customConfigMap) {
        if (CollectionUtils.isEmpty(logList)) {
            return new ArrayList<>();
        }
        // 加工后数据，取所有大模型节点的入参
        // 加工前数据，取开始节点入参和所有工具节点出参
        List<InspectWorkbenchQueryDetailDTO.AidaSignal> groupSignal = new ArrayList<>();
        List<InspectWorkbenchQueryDetailDTO.Signal> beforeSignalList = new ArrayList<>();
        List<InspectWorkbenchQueryDetailDTO.Signal> afterSignalList = new ArrayList<>();
        List<InspectWorkbenchQueryDetailDTO.Signal> inputsLogList = new ArrayList<>();
        Map<String, InnerAppConfigDTO> innerAppConfigMap = new HashMap<>();
        // 只取主流程的信号，因为目前所有要查看的信号都在主流程中
        List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> mainFlowLogList = logList;
        if (StringUtils.isNotBlank(param.getApplicationId())) {
            mainFlowLogList = logList.stream().filter(log -> param.getApplicationId().equals(log.getApplicationId())).collect(Collectors.toList());
        }
        for (InspectWorkbenchQueryDetailDTO.Detail.AidaTrace log : mainFlowLogList) {
            // 获取AI搭配置，将变量key转换为名称，方便运营看懂信号
            Map<String, String> variableMap = getVariableMap(log, innerAppConfigMap, Boolean.FALSE, null);
            // 开始节点，解析入参
            if (START_NODE_TYPE.equals(log.getNodeType())) {
                inputsLogList = getInputsSignalList(log, variableMap);
            }
            // 工具节点，解析出参
            if (API_NODE_TYPE.equals(log.getNodeType())) {
                List<InspectWorkbenchQueryDetailDTO.Signal> apiNodeOutputSignal = getOutputsSignalList(log, variableMap);
                if (CollectionUtils.isNotEmpty(apiNodeOutputSignal)) {
                    beforeSignalList.addAll(apiNodeOutputSignal);
                }
            }
            // 大模型节点，解析入参，进行分组
            if (LLM_NODE_TYPE.equals(log.getNodeType())) {
                // 解析特殊的信号，有些信号包含多个信号信息，但是在一个字段中，需要特殊处理
                List<String> allSignalKey = AidaTraceUtil.getAidaTraceInputKeyList(log);

                // 解析大模型节点的入参，要去掉已经解析过的特殊变量
                List<InspectWorkbenchQueryDetailDTO.Signal> llmNodeInputSignal = getInputsSignalList(log, variableMap, allSignalKey);
                if (CollectionUtils.isNotEmpty(llmNodeInputSignal)) {
                    afterSignalList.addAll(llmNodeInputSignal);
                }
                if (CollectionUtils.isNotEmpty(inputsLogList)) {
                    beforeSignalList.addAll(0, inputsLogList);
                }
                groupSignal.add(new InspectWorkbenchQueryDetailDTO.AidaSignal(log.getNodeId(), log.getNodeName(), beforeSignalList, afterSignalList));
                beforeSignalList = new ArrayList<>();
                afterSignalList = new ArrayList<>();
            }
        }

        // 对比旧值
        if (param.getIsCompareOldValue() != null && param.getIsCompareOldValue()) {
            List<InspectWorkbenchQueryDetailDTO.Signal> oldValueList = getOldValueSort(param);
            for (InspectWorkbenchQueryDetailDTO.AidaSignal aidaSignal : groupSignal) {
                compareOldValueSort(oldValueList, aidaSignal.getSignalAfterList());
            }
        }

        // 重排序
        String config = customConfigMap.get(CustomConfigTypeEnum.SIGNAL_NODE_SORT.getCode());
        if (StringUtils.isNotBlank(config)) {
            // 解析配置
            Map<String, List<InspectWorkbenchQueryDetailDTO.Signal>> nodeSortConfigMap = JSON.parseObject(config, new TypeReference<Map<String, List<InspectWorkbenchQueryDetailDTO.Signal>>>() {
            });

            // 处理每个 AidaSignal
            List<InspectWorkbenchQueryDetailDTO.AidaSignal> afterSort = groupSignal.stream()
                    .peek(signal -> {
                        // 获取配置中的排序
                        List<InspectWorkbenchQueryDetailDTO.Signal> signalSortListConfig = nodeSortConfigMap.getOrDefault(signal.getNodeId(), new ArrayList<>());
                        List<InspectWorkbenchQueryDetailDTO.Signal> signalSortList = processSignal(signal.getSignalAfterList(), signalSortListConfig);
                        // 更新 signal
                        signal.setSignalAfterList(signalSortList);
                    })
                    .collect(Collectors.toList());
            return afterSort;
        }

        return groupSignal;
    }

    @Override
    public Map<String, String> getVariableMap(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace log, Map<String, InnerAppConfigDTO> innerAppConfigMap, Boolean flag, String modelConfigVersionId) {
        if (!NEED_CONVERT_NODE_TYPE.contains(log.getNodeType())) {
            return new HashMap<>();
        }
        InnerAppConfigDTO innerAppConfigDTO;
        if (MapUtils.isNotEmpty(innerAppConfigMap) && innerAppConfigMap.containsKey(log.getApplicationId())) {
            innerAppConfigDTO = innerAppConfigMap.get(log.getApplicationId());
        } else {
            if (flag) {
                innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(log.getApplicationId(), modelConfigVersionId);
            } else {
                innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(log.getApplicationId(), null);
            }
            innerAppConfigMap.put(log.getApplicationId(), innerAppConfigDTO);
        }
        if (START_NODE_TYPE.equals(log.getNodeType())) {
            return getStartNodeInputSignalMap(innerAppConfigDTO);
        }
        if (flag && REPLY_NODE_TYPE.equals(log.getNodeType())) {
            return getEndNodeOutputSignalMap(innerAppConfigDTO);
        }
        if (API_NODE_TYPE.equals(log.getNodeType())) {
            return getApiOutputSignalMap(innerAppConfigDTO, log.getNodeId());
        }
        if (LLM_NODE_TYPE.equals(log.getNodeType())) {
            return getLLmNodeInputSignalMap(innerAppConfigDTO, log.getNodeId(), flag);
        }
        return new HashMap<>();
    }

    private Map<String, String> getEndNodeOutputSignalMap(InnerAppConfigDTO innerAppConfigDTO) {
        return Optional.ofNullable(innerAppConfigDTO)
                .filter(Objects::nonNull)
                .map(InnerAppConfigDTO::getOutputForm)
                .filter(Objects::nonNull)
                .map(InnerAppConfigDTO.OutputForm::getParams)
                .filter(CollectionUtils::isNotEmpty)
                .map(outputParams -> {
                    Map<String, String> nodeInputSignalMap = new HashMap<>();
                    outputParams.forEach(outputParam -> {
                        nodeInputSignalMap.put(outputParam.getKey(), outputParam.getName());
                    });
                    return nodeInputSignalMap;
                }).orElse(new HashMap<>());
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> parseSpecialSignal(String signalValue, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace traceLog) {
        try {
            if (StringUtils.isBlank(signalValue) || !signalValue.startsWith("{")) {
                return new ArrayList<>();
            }

            List<InspectWorkbenchQueryDetailDTO.Signal> signalChildList = new ArrayList<>();

            /**
             * 兼容以下两种case
             * {1:2}\n
             * {1:2}
             *
             * {"1":"2","2":"3"}
             * 特殊逻辑，解析信号，因为有些信号放在输入中，且格式不是json，需要定制化解析
             */
            if (!DataConvertUtil.isValidJson(signalValue)) {
                List<InspectWorkbenchQueryDetailDTO.Signal> signalList = Arrays.stream(signalValue.split("}\n"))
                        .filter(StringUtils::isNotBlank)
                        .map(signal -> {
                            signal = signal.replaceFirst("\\{", "");
                            String[] kv = signal.split(":", 2);  // 只分割第一个 ":"
                            if (kv.length != 2) {
                                return null;
                            }
                            String key = kv[0];
                            String value = kv[1];
                            return new InspectWorkbenchQueryDetailDTO.Signal(key, key, value);
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                // 如果 signalList 不为空，处理最后一个 Signal 的 value
                if (CollectionUtils.isNotEmpty(signalList)) {
                    InspectWorkbenchQueryDetailDTO.Signal lastSignal = signalList.get(signalList.size() - 1);
                    String lastValue = lastSignal.getSignalValue();
                    // 去掉最后一个 Signal 的 value 字符串中的 "}"
                    if (StringUtils.endsWith(lastValue, "}")) {
                        lastSignal.setSignalValue(lastValue.substring(0, lastValue.length() - 1));
                    }
                }
                signalChildList.addAll(signalList);
            }
            return signalChildList;
        } catch (Exception e) {
            log.warn(String.format("解析特殊信息异常, tracelog:%s, specialSignal:%s, errorMsg:%s", JSON.toJSONString(traceLog), signalValue, e.getMessage()), e);
            return new ArrayList<>();
        }
    }

    private void compareOldValue(List<InspectWorkbenchQueryDetailDTO.Signal> oldValueList, List<InspectWorkbenchQueryDetailDTO.Signal> signalList) {
        if (CollectionUtils.isEmpty(oldValueList) || CollectionUtils.isEmpty(signalList)) {
            return;
        }
        Map<String, InspectWorkbenchQueryDetailDTO.Signal> oldValueMap = oldValueList.stream().collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Signal::getSignalName, Function.identity()));
        for (InspectWorkbenchQueryDetailDTO.Signal signal : signalList) {
            if (oldValueMap.containsKey(signal.getSignalName()) && !signal.getSignalValue().equals(oldValueMap.get(signal.getSignalName()).getSignalValue())) {
                signal.setOldValue(oldValueMap.get(signal.getSignalName()).getSignalValue());
            }
        }
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> getOldValue(InspectWorkbenchDetailParam param) {
        InspectWorkbenchQueryDetailDTO lastQueryDetail = getOldQueryDetailDTO(param);
        if (lastQueryDetail != null && CollectionUtils.isNotEmpty(lastQueryDetail.getSignalList())) {
            return lastQueryDetail.getSignalList();
        }
        return new ArrayList<>();
    }

    private static InspectWorkbenchDetailParam buildInspectWorkbenchDetailParam(InspectWorkbenchDetailParam param, InspectWorkbenchSessionDetailDTO lastLlmSession) {
        InspectWorkbenchDetailParam lastLlmParam = new InspectWorkbenchDetailParam();
        lastLlmParam.setSessionId(param.getSessionId());
        lastLlmParam.setMessageId(lastLlmSession.getMessageId());
        lastLlmParam.setPlatform(param.getPlatform());
        lastLlmParam.setLlmSessionIdList(param.getLlmSessionIdList());
        lastLlmParam.setOriginMessage(lastLlmSession.getOriginMessage());
        if (Objects.equals(param.getPlatform(), PlatformTypeEnum.AI.getCode())) {
            lastLlmParam.setWorkspaceId(lastLlmSession.getMessageLevelAidaAppInfo().getWorkspaceId());
            lastLlmParam.setApplicationId(lastLlmSession.getMessageLevelAidaAppInfo().getApplicationId());
            lastLlmParam.setLlmMessageId(lastLlmSession.getMessageLevelAidaAppInfo().getLlmMessageId());
        } else {
            lastLlmParam.setWorkspaceId(param.getWorkspaceId());
            lastLlmParam.setApplicationId(param.getApplicationId());
        }
        lastLlmParam.setIsCompareOldValue(false);
        return lastLlmParam;
    }

    /**
     * 获取输出信号列表
     *
     * @param log 日志信息
     * @return 输出信号列表
     */
    private List<InspectWorkbenchQueryDetailDTO.Signal> getOutputsSignalList(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace log, Map<String, String> variableMap) {
        List<InspectWorkbenchQueryDetailDTO.Signal> outputsLogList = new ArrayList<>();
        String output = log.getOutput();
        if (StringUtils.isNotBlank(output)) {
            JSONObject outputJson = JSONObject.parseObject(output);
            String outputKey = log.getNodeId() + "__complete_content_";
            if (outputJson.containsKey(outputKey)) {
                JSONObject outputs = outputJson.getJSONObject(outputKey);
                List<InspectWorkbenchQueryDetailDTO.Signal> signalList = outputs.entrySet().stream()
                        .map(entry -> {
                            String signalKey = entry.getKey();
                            String signalName = MapUtils.isNotEmpty(variableMap) && variableMap.containsKey(signalKey) ? variableMap.get(signalKey) : signalKey;
                            String signalValue = entry.getValue().toString();
                            return new InspectWorkbenchQueryDetailDTO.Signal(signalKey, signalName, signalValue);
                        })
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(signalList)) {
                    outputsLogList.addAll(signalList);
                }
            }
        }
        return outputsLogList;
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> getInputsSignalList(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace log, Map<String, String> variableMap) {
        return getInputsSignalList(log, variableMap, null);
    }

    /**
     * 获取输入变量信息
     *
     * @param tracelog 执行日志
     * @return 输入变量信息列表
     */
    private List<InspectWorkbenchQueryDetailDTO.Signal> getInputsSignalList(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace tracelog,
                                                                            Map<String, String> variableMap, List<String> allSignalKey) {
        try {
            List<InspectWorkbenchQueryDetailDTO.Signal> inputsLogList = new ArrayList<>();
            String input = tracelog.getInput();
            if (StringUtils.isNotBlank(input)) {
                JSONObject inputJson = JSONObject.parseObject(input);
                if (inputJson.containsKey("inputs")) {
                    // 如果 specialSignalMap 或 variableMap 为空，提前退出
                    boolean hasVariableMap = MapUtils.isNotEmpty(variableMap);

                    JSONObject inputs = inputJson.getJSONObject("inputs");
                    List<InspectWorkbenchQueryDetailDTO.Signal> signalList = inputs.entrySet().stream()
                            .flatMap(entry -> {
                                String signalKey = entry.getKey();
                                String signalValue = entry.getValue().toString();
                                String signalName = hasVariableMap && variableMap.containsKey(signalKey) ? variableMap.get(signalKey) : signalKey;
                                List<InspectWorkbenchQueryDetailDTO.Signal> signalChildList = new ArrayList<>();
                                // 处理特殊信号的逻辑
                                if (CollectionUtils.isNotEmpty(allSignalKey) && allSignalKey.contains(signalKey)) {
                                    signalChildList = parseSpecialSignal(signalValue, tracelog);
                                }

                                // 如果不是特殊信号，则创建默认 SignalSortChild
                                if (CollectionUtils.isNotEmpty(signalChildList)) {
                                    return signalChildList.stream();
                                }
                                return Stream.of(new InspectWorkbenchQueryDetailDTO.Signal(signalKey, signalName, signalValue));
                            })
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(signalList)) {
                        inputsLogList.addAll(signalList);
                    }
                }
            }
            return inputsLogList;
        } catch (Exception e) {
            log.warn(String.format("从trace log中获取普通信号异常, traceLog:%s, variableMap:%s, parsedSignalKeyList:%s, errorMsg:%s", JSON.toJSONString(tracelog), JSON.toJSONString(variableMap), JSON.toJSONString(allSignalKey), e.getMessage()), e);
            return Lists.newArrayList();
        }
    }

    private Map<String, String> getStartNodeInputSignalMap(InnerAppConfigDTO innerAppConfigDTO) {
        if (innerAppConfigDTO == null || CollectionUtils.isEmpty(innerAppConfigDTO.getUserInputFormList())) {
            return new HashMap<>();
        }
        Map<String, String> nodeInputSignalMap = new HashMap<>();
        for (InnerAppConfigDTO.UserInputForm userInputForm : innerAppConfigDTO.getUserInputFormList()) {
            if (userInputForm.getText() != null) {
                nodeInputSignalMap.put(userInputForm.getText().getVariable(), userInputForm.getText().getLabel());
            }
            if (userInputForm.getSelect() != null) {
                nodeInputSignalMap.put(userInputForm.getSelect().getVariable(), userInputForm.getSelect().getLabel());
            }
            if (userInputForm.getParagraph() != null) {
                nodeInputSignalMap.put(userInputForm.getParagraph().getVariable(), userInputForm.getParagraph().getLabel());
            }
            if (userInputForm.getPhoto() != null) {
                nodeInputSignalMap.put(userInputForm.getPhoto().getVariable(), userInputForm.getPhoto().getLabel());
            }
        }
        return nodeInputSignalMap;
    }

    /**
     * 获取大模型节点输入信号映射信息
     *
     * @param innerAppConfigDTO 应用配置信息
     * @param nodeId            节点ID
     * @param flag
     * @return 大模型节点输入信号映射信息
     */
    private Map<String, String> getLLmNodeInputSignalMap(InnerAppConfigDTO innerAppConfigDTO, String nodeId, Boolean flag) {
        JSONObject nodeInfo = getNodeInfo(innerAppConfigDTO, nodeId);
        if (Objects.isNull(nodeInfo)) {
            return new HashMap<>();
        }
        Map<String, String> nodeInputSignalMap = new HashMap<>();
        //为true获取输出
        if (flag) {
            getOutpoutMap(nodeInfo, nodeInputSignalMap);
        }
        if (!nodeInfo.containsKey("user_input_form")) {
            return nodeInputSignalMap;
        }
        JSONArray input = nodeInfo.getJSONArray("user_input_form");
        if (CollectionUtils.isEmpty(input)) {
            return nodeInputSignalMap;
        }

        for (int i = 0; i < input.size(); i++) {
            JSONObject inputItem = input.getJSONObject(i);
            if (inputItem.containsKey("variable") && inputItem.containsKey("label")) {
                nodeInputSignalMap.put(inputItem.getString("variable"), inputItem.getString("label"));
            }
        }
        return nodeInputSignalMap;
    }

    private static void getOutpoutMap(JSONObject nodeInfo, Map<String, String> nodeInputSignalMap) {
        if (nodeInfo.containsKey("structured_form")) {
            JSONObject structuredForm = nodeInfo.getJSONObject("structured_form");
            if (structuredForm.containsKey("output_form")) {
                JSONObject outputForm = structuredForm.getJSONObject("output_form");
                if (outputForm.containsKey("params")) {
                    JSONArray params = outputForm.getJSONArray("params");
                    if (CollectionUtils.isNotEmpty(params)) {
                        for (int i = 0; i < params.size(); i++) {
                            JSONObject outItem = params.getJSONObject(i);
                            if (outItem.containsKey("key") && outItem.containsKey("name")) {
                                nodeInputSignalMap.put(outItem.getString("key"), outItem.getString("name"));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取工具节点输出信号映射信息
     *
     * @param innerAppConfigDTO 应用配置信息
     * @param nodeId            节点ID
     * @return 工具节点输出信号映射信息
     */
    private Map<String, String> getApiOutputSignalMap(InnerAppConfigDTO innerAppConfigDTO, String nodeId) {
        JSONObject nodeInfo = getNodeInfo(innerAppConfigDTO, nodeId);
        if (nodeInfo == null || !nodeInfo.containsKey("output")) {
            return new HashMap<>();
        }
        JSONArray outputList = nodeInfo.getJSONArray("output");
        Map<String, String> nodeInputSignalMap = new HashMap<>();
        for (int i = 0; i < outputList.size(); i++) {
            JSONObject output = outputList.getJSONObject(i);
            if (output.containsKey("key") && output.containsKey("name")) {
                nodeInputSignalMap.put(output.getString("key"), output.getString("name"));
            }
        }
        return nodeInputSignalMap;
    }


    /**
     * 获取节点信息
     *
     * @param innerAppConfigDTO 应用配置信息
     * @param nodeId            节点ID
     * @return 节点信息
     */
    private JSONObject getNodeInfo(InnerAppConfigDTO innerAppConfigDTO, String nodeId) {
        if (innerAppConfigDTO == null || StringUtils.isBlank(innerAppConfigDTO.getNodeConfig())) {
            return null;
        }
        JSONObject nodeConfigJson = DataConvertUtil.tryConvertJson(innerAppConfigDTO.getNodeConfig());
        if (nodeConfigJson == null || !nodeConfigJson.containsKey(nodeId)) {
            return null;
        }
        JSONObject nodeInfo = nodeConfigJson.getJSONObject(nodeId);
        if (!nodeInfo.containsKey("config")) {
            return null;
        }
        return nodeInfo.getJSONObject("config");
    }

    /**
     * 合并结果，构造所有AI搭信号信息
     *
     * @param groupSignal 分组信号
     * @return 所有信号
     */
    private InspectWorkbenchQueryDetailDTO.AidaSignal buildAllSignal(List<InspectWorkbenchQueryDetailDTO.AidaSignal> groupSignal, Map<String, String> customConfigMap) {
        if (CollectionUtils.isEmpty(groupSignal)) {
            return new InspectWorkbenchQueryDetailDTO.AidaSignal();
        }

        InspectWorkbenchQueryDetailDTO.AidaSignal allSignal = new InspectWorkbenchQueryDetailDTO.AidaSignal();
        Map<String, InspectWorkbenchQueryDetailDTO.Signal> beforeSignalMap = new HashMap<>();
        Map<String, InspectWorkbenchQueryDetailDTO.Signal> afterSignalMap = new HashMap<>();
        for (InspectWorkbenchQueryDetailDTO.AidaSignal signal : groupSignal) {
            List<InspectWorkbenchQueryDetailDTO.Signal> beforeSignalList = signal.getSignalBeforeList();
            List<InspectWorkbenchQueryDetailDTO.Signal> afterSignalList = signal.getSignalAfterList();
            if (CollectionUtils.isNotEmpty(beforeSignalList)) {
                for (InspectWorkbenchQueryDetailDTO.Signal beforeSignal : beforeSignalList) {
                    // 如果有重复的信号，覆盖前面的
                    InspectWorkbenchQueryDetailDTO.Signal signalSort = SerializationUtils.clone(beforeSignal);
                    beforeSignalMap.put(beforeSignal.getSignalName(), signalSort);
                }
            }
            if (CollectionUtils.isNotEmpty(afterSignalList)) {
                for (InspectWorkbenchQueryDetailDTO.Signal afterSignal : afterSignalList) {
                    // 如果有重复的信号，覆盖前面的
                    InspectWorkbenchQueryDetailDTO.Signal signalSort = SerializationUtils.clone(afterSignal);
                    afterSignalMap.put(afterSignal.getSignalName(), signalSort);
                }
            }
        }
        allSignal.setSignalBeforeList(new ArrayList<>(beforeSignalMap.values()));

        List<InspectWorkbenchQueryDetailDTO.Signal> signalSortList = new ArrayList<>(afterSignalMap.values());
        // 重排序
        String config = customConfigMap.get(CustomConfigTypeEnum.SIGNAL_ALL_SORT.getCode());
        if (StringUtils.isNotBlank(config)) {
            // 解析配置
            List<InspectWorkbenchQueryDetailDTO.Signal> nodeSortConfigList = JSON.parseArray(config, InspectWorkbenchQueryDetailDTO.Signal.class);
            signalSortList = processSignal(signalSortList, nodeSortConfigList);
        }
        allSignal.setSignalAfterList(signalSortList);
        return allSignal;
    }

    /**
     * 构造基本的信号信息，机器质检回调使用
     *
     * @param allSignal 所有信号
     * @return 信号列表
     */
    private List<InspectWorkbenchQueryDetailDTO.Signal> buildSignalList(InspectWorkbenchQueryDetailDTO.AidaSignal allSignal) {
        return new ArrayList<>(Optional.ofNullable(allSignal)
                .map(InspectWorkbenchQueryDetailDTO.AidaSignal::getSignalAfterList)
                .orElse(Lists.newArrayList()));
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> buildSignalList(InspectWorkbenchTraceDTO inspectWorkbenchTrace) {
        return buildSignalList(null, inspectWorkbenchTrace);
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> buildSignalList(InspectWorkbenchDetailParam param, InspectWorkbenchTraceDTO inspectWorkbenchTrace) {
        List<DialogTraceDTO> traceList = inspectWorkbenchTrace.getTraceList();
        if (CollectionUtils.isEmpty(traceList)) {
            return new ArrayList<>();
        }
        List<DialogTraceDTO> signalList = traceList.stream().filter(trace -> trace.getTraceType() != null && InspectWorkbenchTraceTypeEnum.REFRESH_SLOT.equals(trace.getTraceType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(signalList)) {
            return new ArrayList<>();
        }
        // 转换为map去重
        Map<String, String> signalMap = signalList.stream().flatMap(signal -> {
            List<SlotInfo> slotInfoList = JSONObject.parseArray(signal.getOutput(), SlotInfo.class);
            return slotInfoList.stream().map(slotInfo -> new AbstractMap.SimpleEntry<>(slotInfo.getSlotName(), slotInfo.getSlotValue()));
        }).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (oldValue, newValue) -> oldValue));
        // 转换为结果
        List<InspectWorkbenchQueryDetailDTO.Signal> result = signalMap.entrySet().stream().map(entry -> {
            InspectWorkbenchQueryDetailDTO.Signal signal = new InspectWorkbenchQueryDetailDTO.Signal();
            signal.setSignalKey(entry.getKey());
            signal.setSignalName(entry.getKey());
            signal.setSignalValue(entry.getValue());
            return signal;
        }).collect(Collectors.toList());
        // 对比旧值
        if (param != null && param.getIsCompareOldValue() != null && param.getIsCompareOldValue()) {
            compareOldValue(getOldValue(param), result);
        }
        return result;
    }

    private InspectWorkbenchQueryDetailDTO.Detail buildDetailInfo(List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return null;
        }
        InspectWorkbenchQueryDetailDTO.Detail detail = new InspectWorkbenchQueryDetailDTO.Detail();
        detail.setBasicInfo(buildAidaBasicInfo(logList));
        detail.setAidaTraceList(logList);
        return detail;
    }

    private List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> buildAidaBasicInfo(List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList) {
        List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfoList = new ArrayList<>();
        // 取第一个开始节点
        InspectWorkbenchQueryDetailDTO.Detail.AidaTrace startNode = logList.stream().filter(log -> START_NODE_TYPE.equals(log.getNodeType())).findFirst().orElse(null);
        if (startNode != null) {
            String input = startNode.getInput();
            if (StringUtils.isNotBlank(input)) {
                JSONObject inputJson = JSONObject.parseObject(input);
                if (inputJson.containsKey("query")) {
                    basicInfoList.add(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("输入", inputJson.getString("query")));
                }
            }
        }
        // 取最后一个回复节点
        InspectWorkbenchQueryDetailDTO.Detail.AidaTrace replyNode = logList.stream().filter(log -> REPLY_NODE_TYPE.equals(log.getNodeType())).reduce((first, second) -> second).orElse(null);
        if (replyNode != null) {
            basicInfoList.add(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("输出", replyNode.getOutput()));
        }
        // 取执行动作
        String actionConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_ACTION_NODE_INFO);
        if (StringUtils.isNotBlank(actionConfig)) {
            // AI搭的工具节点是通用能力，无法得知哪个节点是用来执行动作的，因此将动作节点配置在Lion上
            Map<String, Map<String, String>> actionMap = JSONObject.parseObject(actionConfig, new TypeReference<Map<String, Map<String, String>>>() {
            });
            Map<String, Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> logMap = logList.stream()
                    .collect(Collectors.groupingBy(
                            InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getApplicationId,
                            Collectors.toMap(
                                    InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getNodeId,
                                    Function.identity(), (a, b) -> a
                            )
                    ));
            List<String> actionList = new ArrayList<>();
            for (Map.Entry<String, Map<String, String>> entry : actionMap.entrySet()) {
                String applicationId = entry.getKey();
                Map<String, String> actionInfo = entry.getValue();
                if (logMap.containsKey(applicationId)) {
                    Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> appNodeLog = logMap.get(applicationId);
                    for (Map.Entry<String, String> actionEntry : actionInfo.entrySet()) {
                        String nodeId = actionEntry.getKey();
                        if (appNodeLog.containsKey(nodeId)) {
                            String actionName = StringUtils.isNotBlank(actionEntry.getValue()) ? actionEntry.getValue() : appNodeLog.get(nodeId).getNodeName();
                            actionList.add(actionName);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(actionList)) {
                basicInfoList.add(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("执行动作", String.join(",", actionList)));
            }
        }

        return basicInfoList;
    }

    private InspectWorkbenchQueryDetailDTO.Detail buildDetailInfo(InspectWorkbenchTraceDTO inspectWorkbenchTrace) {
        List<DialogTraceDTO> traceList = inspectWorkbenchTrace.getTraceList();
        InspectWorkbenchQueryDetailDTO.Detail detail = new InspectWorkbenchQueryDetailDTO.Detail();
        if (CollectionUtils.isEmpty(traceList)) {
            return detail;
        }
        // 把多次调用进行分组
        List<List<DialogTraceDTO>> invokeTraceList = new ArrayList<>();
        List<DialogTraceDTO> curGroupTraceList = new ArrayList<>();
        for (DialogTraceDTO trace : traceList) {
            curGroupTraceList.add(trace);
            if (InspectWorkbenchTraceTypeEnum.OUT_SERVICE.equals(trace.getTraceType())) {
                invokeTraceList.add(curGroupTraceList);
                curGroupTraceList = new ArrayList<>();
            }
        }
        detail.setBasicInfo(buildBasicInfo(invokeTraceList, inspectWorkbenchTrace.getTraceId()));
        detail.setTraceList(buildTraceList(invokeTraceList));
        return detail;
    }

    private List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> buildBasicInfo(List<List<DialogTraceDTO>> invokeTraceList, String traceId) {
        List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfoList = new ArrayList<>();
        for (int i = 0; i < invokeTraceList.size(); i++) {
            List<DialogTraceDTO> traceList = invokeTraceList.get(i);
            DialogTraceDTO invokeTrace = traceList.stream().filter(trace -> trace.getTraceType() != null && InspectWorkbenchTraceTypeEnum.OUT_SERVICE.equals(trace.getTraceType())).findFirst().orElse(null);
            if (invokeTrace == null) {
                continue;
            }
            DialogRequest dialogRequest = JSONObject.parseObject(invokeTrace.getInput(), DialogRequest.class);
            DialogResponse dialogResponse = JSONObject.parseObject(invokeTrace.getOutput(), DialogResponse.class);
            // 首句是输入和输出
            if (i == 0) {
                // 第一次一定是由上游触发，包含由输入，从history字段中拿
                List<MessageHistoryItem> messageHistoryList = dialogRequest.getMessageHistory();
                if (CollectionUtils.isNotEmpty(messageHistoryList)) {
                    MessageHistoryItem lastMessage = messageHistoryList.get(messageHistoryList.size() - 1);
                    // 获取最后一个用户的输入话术作为当前输入
                    if (lastMessage != null && lastMessage.getRole() != null && lastMessage.getRole().equals(DialogRoleEnum.CUSTOMER)) {
                        InspectWorkbenchQueryDetailDTO.Detail.BasicInfo basicInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo();
                        basicInfo.setDisplayName("输入");
                        basicInfo.setDisplayContent(lastMessage.getMessage());
                        basicInfoList.add(basicInfo);
                    }
                }
                // 输出取当前话术
                InspectWorkbenchQueryDetailDTO.Detail.BasicInfo basicInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo();
                basicInfo.setDisplayName("输出");
                // 不同状态码代表不同的返回结果
                if (dialogResponse != null && dialogResponse.getDialogResultCodeEnum() != null && dialogResponse.getDialogResultCodeEnum() == DialogResultCodeEnum.SUCCESS) {
                    if (ReplyEndTypeEnum.INTERRUPT.getCode().equals(dialogResponse.getEndType())) {
                        basicInfo.setDisplayContent("被打断");
                    } else if (ReplyEndTypeEnum.FUSE_END.getCode().equals(dialogResponse.getEndType())) {
                        basicInfo.setDisplayContent("熔断结束：" + dialogResponse.getEndMessage());
                    } else if (ReplyEndTypeEnum.EMPTY_MESSAGE.getCode().equals(dialogResponse.getEndType())) {
                        basicInfo.setDisplayContent("结果为空");
                    } else {
                        if (CollectionUtils.isNotEmpty(dialogResponse.getDmResultList()) && dialogResponse.getDmResultList().get(0) != null && StringUtils.isNotBlank(dialogResponse.getDmResultList().get(0).getSolution())) {
                            basicInfo.setDisplayContent(dialogResponse.getDmResultList().get(0).getSolution());
                        }
                    }
                } else {
                    basicInfo.setDisplayContent("解析trace输出时发生异常");
                }
                basicInfoList.add(basicInfo);
                // 执行动作
                List<DialogTraceDTO> actionTraceList = traceList.stream().filter(trace -> trace.getTraceType() != null && InspectWorkbenchTraceTypeEnum.INTERFACE.equals(trace.getTraceType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(actionTraceList)) {
                    basicInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo();
                    basicInfo.setDisplayName("执行动作");
                    StringBuilder actionContent = new StringBuilder();
                    for (DialogTraceDTO actionTrace : actionTraceList) {
                        try {
                            JSONArray jsonArray = JSONObject.parseArray(actionTrace.getProcess());
                            if (jsonArray != null) {
                                for (int j = 0; j < jsonArray.size(); j++) {
                                    JSONObject jsonObject = jsonArray.getJSONObject(j);
                                    if (jsonObject != null && jsonObject.containsKey("api_name")) {
                                        actionContent.append(jsonObject.getString("api_name")).append("  ");
                                    }
                                }
                            }
                        } catch (JSONException e) {
                            // 捕获JSON解析错误，不做任何处理
                            log.warn("解析执行动作失败, actionTrace:{}", JSONObject.toJSONString(actionTrace), e);
                        }
                    }
                    basicInfo.setDisplayContent(actionContent.toString());
                    basicInfoList.add(basicInfo);
                }
            } else if (dialogResponse != null && CollectionUtils.isNotEmpty(dialogResponse.getDmResultList()) && dialogResponse.getDmResultList().get(0) != null && StringUtils.isNotBlank(dialogResponse.getDmResultList().get(0).getSolution())) {
                // 第二次以后的调用一定是连续推送触发，只有输出
                InspectWorkbenchQueryDetailDTO.Detail.BasicInfo basicInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo();
                basicInfo.setDisplayName("推送");
                basicInfo.setDisplayContent(dialogResponse.getDmResultList().get(0).getSolution());
                basicInfoList.add(basicInfo);
            }
        }
        // 增加调用trace
        InspectWorkbenchQueryDetailDTO.Detail.BasicInfo basicInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo();
        basicInfo.setDisplayName("TraceID");
        basicInfo.setDisplayContent(traceId);
        basicInfoList.add(basicInfo);
        return basicInfoList;
    }

    private List<InspectWorkbenchQueryDetailDTO.Detail.Trace> buildTraceList
            (List<List<DialogTraceDTO>> invokeTraceList) {
        if (CollectionUtils.isEmpty(invokeTraceList)) {
            return new ArrayList<>();
        }
        List<InspectWorkbenchQueryDetailDTO.Detail.Trace> resultTraceList = new ArrayList<>();
        for (int i = 0; i < invokeTraceList.size(); i++) {
            List<DialogTraceDTO> traceList = invokeTraceList.get(i);
            List<DialogTraceDTO> displayTraceList = traceList.stream().filter(trace -> trace.getTraceType() != null && DISPLAY_TRACE_TYPE.contains(trace.getTraceType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(displayTraceList)) {
                // 第一次调用是上游触发，一定展示
                if (i == 0) {
                    resultTraceList.addAll(displayTraceList.stream().map(this::convertTrace).collect(Collectors.toList()));
                } else {
                    // 第二次以后调用是连续推送，需要判断是否产生了推送，如果没有推送，就不展示trace信息
                    DialogTraceDTO invokeTrace = traceList.stream().filter(trace -> trace.getTraceType() != null && InspectWorkbenchTraceTypeEnum.OUT_SERVICE.equals(trace.getTraceType())).findFirst().orElse(null);
                    if (invokeTrace != null && StringUtils.isNotBlank(invokeTrace.getOutput())) {
                        DialogResponse dialogResponse = JSONObject.parseObject(invokeTrace.getOutput(), DialogResponse.class);
                        if (dialogResponse != null && CollectionUtils.isNotEmpty(dialogResponse.getDmResultList()) && dialogResponse.getDmResultList().get(0) != null && StringUtils.isNotBlank(dialogResponse.getDmResultList().get(0).getSolution())) {
                            resultTraceList.addAll(displayTraceList.stream().map(this::convertTrace).collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
        return resultTraceList;
    }

    private InspectWorkbenchQueryDetailDTO.Detail.Trace convertTrace(DialogTraceDTO dialogTrace) {
        InspectWorkbenchQueryDetailDTO.Detail.Trace trace = new InspectWorkbenchQueryDetailDTO.Detail.Trace();
        trace.setType(String.valueOf(dialogTrace.getTraceType().getCode()));
        if (!"{}".equals(dialogTrace.getInput())) {
            trace.setInput(DataConvertUtil.deserializeRecursion(dialogTrace.getInput()));
        }
        trace.setProcess(DataConvertUtil.deserializeRecursion(dialogTrace.getProcess()));
        if (dialogTrace.getTraceType() != null && dialogTrace.getTraceType().equals(InspectWorkbenchTraceTypeEnum.LLM) && StringUtils.isNotBlank(dialogTrace.getModel())) {
            Map<String, String> modelMap = new HashMap<>();
            modelMap.put("model", dialogTrace.getModel());
            trace.setModel(JSONObject.toJSONString(modelMap));
        }
        trace.setOutput(DataConvertUtil.deserializeRecursion(dialogTrace.getOutput()));
        return trace;
    }

    private InspectWorkbenchSessionDetailDTO convertSession(MessageDTO message) {
        InspectWorkbenchSessionDetailDTO inspectWorkbenchDetail = new InspectWorkbenchSessionDetailDTO();
        inspectWorkbenchDetail.setTime(message.getAddTime());
        inspectWorkbenchDetail.setMessageType(message.getType());
        inspectWorkbenchDetail.setMessageId(String.valueOf(message.getId()));
        inspectWorkbenchDetail.setOriginMessage(JSONObject.toJSONString(message));
        List<String> userTypeList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.USER_MESSAGE_TYPE_LIST, String.class, DEFAULT_USER_TYPE);
        inspectWorkbenchDetail.setSenderType(userTypeList.contains(message.getSenderType()) ? RoleEnum.USER.getCode() : RoleEnum.ASSISTANT.getCode());
        appendMessageInfo(inspectWorkbenchDetail, message);
        return inspectWorkbenchDetail;
    }

    /**
     * 转换IVR会话
     *
     * @param ivrSessionInfoDTO
     * @return
     */
    private List<InspectWorkbenchSessionDetailDTO> convertIvrSession(IvrSessionInfoDTO ivrSessionInfoDTO) {
        List<Integer> filerOperationTypeCodeList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_IVR_FILTER_MESSAGE_TYPE, Integer.class,
                Lists.newArrayList(OperationTypeEnum.VOICE_BROADCAST.getCode(), OperationTypeEnum.CONTENT_INPUT.getCode(), OperationTypeEnum.TRANSFER_STAFF.getCode()));
        List<InspectWorkbenchSessionDetailDTO> inspectWorkbenchDetailList = new ArrayList<>();

        List<PathInfoDTO> pathInfoList = ivrSessionInfoDTO.getPathInfo();
        // 从PathInfo获取大模型组件的moduleVisitId
        List<String> llmModuleVisitIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pathInfoList)) {
            for (PathInfoDTO pathInfo : pathInfoList) {
                if (pathInfo.getModuleTypeCode() != null && ModuleTypeCodeEnum.LLM.getCode().equals(pathInfo.getModuleTypeCode())) {
                    llmModuleVisitIdList.add(pathInfo.getModuleVisitId());
                }
            }
        }
        List<String> filerBroadcastPurposeList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_IVR_FILTER_BROADCAST_PURPOSE, String.class,
                Lists.newArrayList("TRANSFER_STAFF_VOICE", "WAIT_TONE"));
        // 获取有效的消息列表，填充llm标记
        List<UserInteractiveInfoDTO> userInteractiveInfoList = ivrSessionInfoDTO.getUserInteractiveInfo();
        for (UserInteractiveInfoDTO userInteractiveInfoDTO : userInteractiveInfoList) {
            List<UserInteractiveInfoDTO.ModuleDTO> modules = userInteractiveInfoDTO.getModules();
            if (CollectionUtils.isEmpty(modules)) {
                continue;
            }

            for (UserInteractiveInfoDTO.ModuleDTO module : modules) {
                List<UserInteractiveInfoDTO.OperationDTO> operations = module.getOperations();
                if (CollectionUtils.isNotEmpty(operations)) {
                    // 转换为消息
                    for (UserInteractiveInfoDTO.OperationDTO operationDTO : module.getOperations()) {
                        // 过滤掉不支持的操作类型
                        if (!filerOperationTypeCodeList.contains(operationDTO.getOperationTypeCode())) {
                            continue;
                        }
                        OperationTypeEnum operationTypeEnum = OperationTypeEnum.getByCode(operationDTO.getOperationTypeCode());
                        if (operationTypeEnum == null) {
                            continue;
                        }
                        InspectWorkbenchSessionDetailDTO inspectWorkbenchDetail = new InspectWorkbenchSessionDetailDTO();
                        inspectWorkbenchDetail.setTime(operationDTO.getOperationStartTime());
                        inspectWorkbenchDetail.setMessageType(operationTypeEnum.getValue());
                        inspectWorkbenchDetail.setMessageId(String.valueOf(operationDTO.getOperationId()));
                        inspectWorkbenchDetail.setOriginMessage(JSONObject.toJSONString(operationDTO));
                        inspectWorkbenchDetail.setSenderType(RoleEnum.USER.getCode() == (operationDTO.getOperationRole()) ? RoleEnum.USER.getCode() : RoleEnum.ASSISTANT.getCode());

                        switch (operationTypeEnum) {
                            case VOICE_BROADCAST:
                                VoiceBroadcaseDTO voiceBroadcaseDTO = JSON.parseObject(operationDTO.getExtraMap(), VoiceBroadcaseDTO.class);
                                inspectWorkbenchDetail.setMessage(voiceBroadcaseDTO.getVoiceTxt());
                                if (llmModuleVisitIdList.contains(module.getModuleVisitId())) {
                                    // 注意：需要过滤掉，转人工的播报语音。"broadcastPurpose\":\"TRANSFER_STAFF_VOICE
                                    if (StringUtils.isNotBlank(voiceBroadcaseDTO.getBroadcastPurpose())
                                            && filerBroadcastPurposeList.contains(voiceBroadcaseDTO.getBroadcastPurpose())) {
                                        inspectWorkbenchDetail.setIsLlm(Boolean.FALSE);
                                    } else {
                                        inspectWorkbenchDetail.setIsLlm(Boolean.TRUE);
                                    }
                                }
                                break;
                            case CONTENT_INPUT:
                                ContentInputDTO contentInputDTO = JSON.parseObject(operationDTO.getExtraMap(), ContentInputDTO.class);
                                if (IvrContentInputTypeEnum.VOICE.name().equals(contentInputDTO.getInputType())) {
                                    inspectWorkbenchDetail.setMessage(contentInputDTO.getAsrText());
                                } else if (IvrContentInputTypeEnum.DTMF.name().equals(contentInputDTO.getInputType())) {
                                    inspectWorkbenchDetail.setMessage(contentInputDTO.getInputValue());
                                }
                                break;
                            case TRANSFER_STAFF:
                                // 转人工类型，暂时不需要处理，透传给前端就行
                                inspectWorkbenchDetail.setMessage(operationDTO.getOperationTypeName());
                                break;
                            default:
                                break;
                        }
                        inspectWorkbenchDetailList.add(inspectWorkbenchDetail);
                    }
                }
            }
        }
        return inspectWorkbenchDetailList;
    }


    private boolean checkIfLlm(JSONObject jsonObject, String message) {
        if (jsonObject.containsKey("extendInfo")) {
            JSONObject extendInfo = jsonObject.getJSONObject("extendInfo");
            boolean isLlm = extendInfo != null && "1".equals(extendInfo.getString("llm"));
            if (isLlm) {
                List<String> filterMessageList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.LLM_FILTER_MESSAGE, String.class);
                if (CollectionUtils.isNotEmpty(filterMessageList) && StringUtils.isNotBlank(message)) {
                    isLlm = !filterMessageList.contains(message);
                }
                return isLlm;
            }
        }

        return false;
    }

    private void appendMessageInfo(InspectWorkbenchSessionDetailDTO inspectWorkbenchDetail, MessageDTO message) {
        StringBuilder messageBuilder = new StringBuilder();
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotBlank(message.getData())) {
            jsonObject = JSONObject.parseObject(message.getData());
        }
        boolean isLlm = false;
        // 消息处理器
        BiConsumer<StringBuilder, String> handle;
        try {
            // 先试使用自定义的解析方法，如果没有，使用代码定义的解析逻辑，如果未定义，走兜底解析逻辑
            handle = ChatMessageTypeEnum.getCustomizeHandle(message.getType());
            if (handle == null) {
                ChatMessageTypeEnum messageType = ChatMessageTypeEnum.getByName(message.getType());
                if (messageType != null) {
                    handle = messageType.getHandler();
                } else {
                    handle = ChatMessageTypeEnum.getDefaultHandler();
                }
            }
        } catch (Exception e) {
            log.warn("获取处理器失败, message:{}", JSONObject.toJSONString(message), e);
            handle = ChatMessageTypeEnum.getDefaultHandler();
        }
        // 处理消息
        handle.accept(messageBuilder, message.getData());
        inspectWorkbenchDetail.setMessage(messageBuilder.toString());
        // 特殊处理 FAQ_ANSWER 类型，判断是否是大模型路径
        if (ChatMessageTypeEnum.FAQ_ANSWER.getName().equals(message.getType()) || ChatMessageTypeEnum.TEXT.getName().equals(message.getType())) {
            isLlm = checkIfLlm(jsonObject, inspectWorkbenchDetail.getMessage());
            if (!isLlm) {
                List<String> llmSenderTypeList = Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_LLM_SENDER_TYPE_LIST, String.class);
                isLlm = llmSenderTypeList.contains(message.getSenderType());
            }
        }
        inspectWorkbenchDetail.setIsLlm(isLlm);
    }

    private List<InspectWorkbenchDTO> convertPbSession(List<DialogSessionInfoDTO> pageData) {
        if (CollectionUtils.isEmpty(pageData)) {
            return new ArrayList<>();
        }
        List<String> sessionIdList = pageData.stream().map(DialogSessionInfoDTO::getSessionId).collect(Collectors.toList());
        List<InspectWorkbenchEvalPo> inspectWorkbenchEvalList = inspectWorkbenchEvalGeneratorService.getBySessionIdList(sessionIdList);
        Map<String, List<InspectWorkbenchEvalPo>> sessionMap = inspectWorkbenchEvalList.stream().collect(Collectors.groupingBy(InspectWorkbenchEvalPo::getSessionId));
        return pageData.stream().map(dialogSessionInfo -> {
            List<InspectWorkbenchEvalPo> evalList = sessionMap.get(dialogSessionInfo.getSessionId());
            return convertPbSession(dialogSessionInfo, evalList);
        }).collect(Collectors.toList());
    }

    private InspectWorkbenchDTO convertPbSession(DialogSessionInfoDTO session, List<InspectWorkbenchEvalPo> evalList) {
        InspectWorkbenchDTO inspectWorkbench = new InspectWorkbenchDTO();
        inspectWorkbench.setId(Collections.singletonList(session.getId()));
        inspectWorkbench.setSessionId(session.getSessionId());
        inspectWorkbench.setTime(session.getTime());
        inspectWorkbench.setUserId(session.getUserId());
        inspectWorkbench.setOrderId(session.getOrderId());
        inspectWorkbench.setPlatformType(ThirdPlatformTypeEnum.PB.getCode());
        inspectWorkbench.setTurn(session.getTurn());
        inspectWorkbench.setChannel(ChannelEnum.ONLINE.getCode());
        return inspectWorkbench;
    }

    private List<InspectWorkbenchDTO> convertAidaSession(List<AidaSessionInfoDTO> sessionInfo, InspectWorkbenchConditionParam condition, Future<List<String>> future) {
        return Collections.singletonList(mergeAidaSession(sessionInfo, condition, future));
    }

    /**
     * 转换成前端需要的dto
     *
     * @param sessionInfoList
     * @param condition
     * @param future
     * @return
     */
    private InspectWorkbenchDTO mergeAidaSession(List<AidaSessionInfoDTO> sessionInfoList, InspectWorkbenchConditionParam condition, Future<List<String>> future) {
        AidaSessionInfoDTO sessionInfo = sessionInfoList.get(0);
        InspectWorkbenchDTO dto = new InspectWorkbenchDTO();
        dto.setSessionId(sessionInfo.getSessionId());
        dto.setUserId(sessionInfo.getUserId());
        dto.setOrderId(sessionInfo.getOrderId());
        dto.setApplicationId(sessionInfo.getApplicationId());
        dto.setApplicationName(sessionInfo.getApplicationName());
        dto.setPlatformType(PlatformTypeEnum.AI.getCode());
        dto.setWorkspaceId(sessionInfo.getWorkspaceId());
        dto.setWorkspaceName(sessionInfo.getWorkspaceName());
        mergeAidaSessionInfo(sessionInfoList, dto, condition, future);
        dto.setVersionId(sessionInfo.getAppModelVersionId());
        dto.setTime(new Date(sessionInfo.getTime()));
        dto.setChannel(sessionInfo.getChannel());
        return dto;
    }

    private void mergeAidaSessionInfo(List<AidaSessionInfoDTO> sessionInfoList, InspectWorkbenchDTO dto, InspectWorkbenchConditionParam condition, Future<List<String>> future) {
        // 补充子流程的conversationId
        List<String> conversationIdList = sessionInfoList.stream().map(AidaSessionInfoDTO::getId).collect(Collectors.toList());
        dto.setId(getAllConversationId(conversationIdList, sessionInfoList.get(0).getApplicationId(), condition, future));
        // 取轮次之和
        int turn = sessionInfoList.stream().mapToInt(AidaSessionInfoDTO::getTurn).sum();
        dto.setTurn(turn);
    }

    private List<String> getAllConversationId(List<String> curConversationIdList, String applicationId, InspectWorkbenchConditionParam condition, Future<List<String>> future) {
        List<String> subApplicationConversationIdList = new ArrayList<>();
        boolean isEmpty = false;
        if (future != null) {
            try {
                subApplicationConversationIdList = future.get(30, TimeUnit.SECONDS);
                isEmpty = true;
            } catch (Exception e) {
                Cat.logError(e);
                log.error("获取子流程conversationId异常,applicationId={}", applicationId, e);
            }
        }
        // 没有执行异步操作，再重新获取一遍，isEmpty为true表示走过异步逻辑，无需再走一遍
        if (!isEmpty && CollectionUtils.isEmpty(subApplicationConversationIdList)) {
            subApplicationConversationIdList = getSubApplicationConversationId(condition.getSessionId(), condition.getWorkspaceId(), applicationId, condition.getStartTime(), condition.getEndTime());
        }
        Set<String> conversationIdList = new HashSet<>(curConversationIdList);
        if (CollectionUtils.isNotEmpty(subApplicationConversationIdList)) {
            conversationIdList.addAll(subApplicationConversationIdList);
        }
        return new ArrayList<>(conversationIdList);
    }

    /**
     * 获取子流程的conversationId列表
     *
     * @param sessionId     会话ID
     * @param workspaceId   工作空间ID
     * @param applicationId 应用ID
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 子流程的conversationId列表
     */
    private List<String> getSubApplicationConversationId(String sessionId, String workspaceId, String applicationId, Date startTime, Date endTime) {
        String subApplication = Lion.getString(ConfigUtil.getAppkey(), LionConstants.SUB_APPLICATION_CONFIG);
        if (StringUtils.isNotBlank(subApplication)) {
            Map<String, List<String>> subApplicationMap = JSONObject.parseObject(subApplication, new TypeReference<Map<String, List<String>>>() {
            });
            if (MapUtils.isNotEmpty(subApplicationMap) && subApplicationMap.containsKey(applicationId)) {
                List<String> subApplicationList = subApplicationMap.get(applicationId);
                PageParam<InspectWorkbenchConditionParam> curParam = new PageParam<>();
                InspectWorkbenchConditionParam curCondition = new InspectWorkbenchConditionParam();
                curCondition.setSessionId(sessionId);
                curCondition.setWorkspaceId(workspaceId);
                curCondition.setStartTime(startTime);
                curCondition.setEndTime(endTime);
                curParam.setCondition(curCondition);
                //
                List<AidaSessionInfoDTO> aidaDataList = switchQueryStrategy(subApplicationList, curParam);
//                        aidaInvokeServiceProxy.listByAppIdsAndSessionId(subApplicationList, curParam);
                if (CollectionUtils.isNotEmpty(aidaDataList)) {
                    return aidaDataList.stream().map(AidaSessionInfoDTO::getId).collect(Collectors.toList());
                }
            }
        }
        return new ArrayList<>();
    }

    /**
     * 查询会话convercationId的切换逻辑
     *
     * @param applicationIdList
     * @param pageCondition
     * @return
     */
    private List<AidaSessionInfoDTO> switchQueryStrategy(List<String> applicationIdList, PageParam<InspectWorkbenchConditionParam> pageCondition) {
        InspectWorkbenchConditionParam condition = pageCondition.getCondition();
        AidaMessagesConditionParam conditionParam = new AidaMessagesConditionParam();

        conditionParam.setApplicationIdList(applicationIdList);
        conditionParam.setSessionId(condition.getSessionId());
        conditionParam.setStartTime(condition.getStartTime());
        conditionParam.setEndTime(condition.getEndTime());

        if (condition.getEndTime() == null || condition.getStartTime() == null) {
            Date endDate = DateUtil.getEndOfDay(new Date());
            conditionParam.setEndTime(endDate);
            Integer aidaMessageDays = aidaInvokeServiceProxy.getAidaMessageDays();
            conditionParam.setStartTime(DateUtil.getStartOfDay(DateUtil.addDays(endDate, -aidaMessageDays)));
        }


        List<AidaMessagesPo> aidaMessagesPoList = aidaMessagesGeneratorService.listByCondition(conditionParam);

        if (CollectionUtils.isEmpty(aidaMessagesPoList)) {
            log.info("{}-不走AIDA根据appIds批量查询, 查询到数据, request ={}", "WORKBENCH", JSON.toJSONString(conditionParam));
            return Collections.emptyList();
        }

        // 如果未指定applicationIdList需要
        if (CollectionUtils.isEmpty(applicationIdList)) {
            applicationIdList = aidaMessagesPoList.stream()
                    .map(AidaMessagesPo::getAppId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<AppDTO> apps = aidaInvokeServiceProxy.listAidaAppDtoByIds(applicationIdList);
        if (CollectionUtils.isEmpty(apps)) {
            return Collections.emptyList();
        }

        Map<String, AppDTO> appMap = apps.stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));
        Map<String, TenantDTO> tenantMap = aidaInvokeServiceProxy.listAidaTenantDtoByIds(apps.stream().map(AppDTO::getTenantId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(TenantDTO::getId, Function.identity()));

        return aidaMessagesPoList.stream()
                .sorted(Comparator.comparing(AidaMessagesPo::getCreatedAt))
                .collect(Collectors.groupingBy(AidaMessagesPo::getConversationId))
                .entrySet().stream()
                .map(entry -> {
                    AidaMessagesPo messages = entry.getValue().get(0);
                    AidaSessionInfoDTO aidaSessionInfoDTO = new AidaSessionInfoDTO();
                    aidaSessionInfoDTO.setId(entry.getKey());
                    aidaSessionInfoDTO.setSessionId(messages.getSessionId());
                    aidaSessionInfoDTO.setApplicationId(messages.getAppId());
                    AppDTO app = appMap.get(messages.getAppId());
                    aidaSessionInfoDTO.setApplicationName(app.getName());
                    TenantDTO tenants = tenantMap.get(app.getTenantId());
                    aidaSessionInfoDTO.setWorkspaceId(tenants.getId());
                    aidaSessionInfoDTO.setWorkspaceName(tenants.getName());
                    aidaSessionInfoDTO.setTurn(entry.getValue().size());
                    aidaSessionInfoDTO.setTime(messages.getCreatedAt().getTime());
                    aidaSessionInfoDTO.setUserId(messages.getUserId());
                    aidaSessionInfoDTO.setOrderId(messages.getOrderId());
                    aidaSessionInfoDTO.setAppModelVersionId(parseVersionId(messages.getCommonData()));
                    aidaSessionInfoDTO.setChannel(WorkbenchDataProcessor.parseChannelCode(messages.getExtraInfo()));
                    return aidaSessionInfoDTO;
                })
                .collect(Collectors.toList());

    }

    /**
     * 从commonData中解析应用的版本id
     *
     * @param commonData
     * @return
     */
    private String parseVersionId(String commonData) {
        if (StringUtils.isBlank(commonData)) {
            return null;
        }
        CommonDataDTO commonDataDTO = JSON.parseObject(commonData, new TypeReference<CommonDataDTO>() {
        });
        if (Objects.isNull(commonDataDTO) || StringUtils.isBlank(commonDataDTO.getAppModelVersionId())) {
            return null;
        }
        return commonDataDTO.getAppModelVersionId();
    }

    /**
     * 填充点赞点踩信息
     *
     * @param inspectWorkbenchDTOList 质检信息列表，需要包含SessionId
     */
    private void appendEvalStatus(List<InspectWorkbenchDTO> inspectWorkbenchDTOList) {
        // 列出sessionId列表
        List<String> sessionIdList = inspectWorkbenchDTOList.stream().map(InspectWorkbenchDTO::getSessionId).collect(Collectors.toList());
        // 查询质检工作台评价表，获取点赞点踩信息
        List<InspectWorkbenchEvalPo> inspectWorkbenchEvalPoList = inspectWorkbenchEvalGeneratorService.getBySessionIdList(sessionIdList);
        // 根据sessionId分组，得到每组的点赞点踩信息
        Map<String, List<InspectWorkbenchEvalPo>> sessionMap = inspectWorkbenchEvalPoList.stream().collect(Collectors.groupingBy(InspectWorkbenchEvalPo::getSessionId));

        // 查询质检信息detail
        List<WorkbenchInspectInfoPo> inspectInfoList = workbenchInspectInfoGeneratorService.listBySessionIds(sessionIdList);

        // 对inpectInfoList过滤platformType为null的
        inspectInfoList = inspectInfoList.stream().filter(workbenchInspectInfoPo -> workbenchInspectInfoPo.getPlatformType() != null).collect(Collectors.toList());
        // 对inspectInfoList按照platformType分组, 分别得到AIDA和PB的质检结果
        Map<Integer, List<WorkbenchInspectInfoPo>> platformTypeMap = inspectInfoList.stream().collect(Collectors.groupingBy(WorkbenchInspectInfoPo::getPlatformType));

        // 获取aida的质检结果
        List<WorkbenchInspectInfoPo> aidaInspectInfoList = platformTypeMap.getOrDefault(PlatformTypeEnum.AI.getCode(), Collections.emptyList());
        // 对inspectInfoList进行过滤，只保留人工质检结果，以及message_id非null的, 以及dimension为1(query维度)的
        aidaInspectInfoList = aidaInspectInfoList.stream()
                .filter(workbenchInspectInfoPo -> MetricEvalTypeEnum.MANUAL.getCode() == workbenchInspectInfoPo.getType()
                        && StringUtils.isNotBlank(workbenchInspectInfoPo.getLlmMessageId())
                        && Integer.valueOf(DimensionTypeEnum.QUERY.getCode()).equals(workbenchInspectInfoPo.getDimension()))
                .collect(Collectors.toList());
        // 多个质检指标的情况下取最新质检指标结果
        Map<String, WorkbenchInspectInfoPo> aidaInspectInfoMap = aidaInspectInfoList.stream()
                .collect(Collectors.toMap(WorkbenchInspectInfoPo::getSessionId, Function.identity(), (a, b) -> a.getGmtModified().after(b.getGmtModified()) ? a : b));

        // 获取pb的质检结果
        List<WorkbenchInspectInfoPo> pbInspectInfoList = platformTypeMap.getOrDefault(PlatformTypeEnum.PB.getCode(), Collections.emptyList());
        // 对inspectInfoList进行过滤，只保留人工质检结果，以及message_id非null的, 以及dimension为1(query维度)的
        pbInspectInfoList = pbInspectInfoList.stream()
                .filter(workbenchInspectInfoPo -> MetricEvalTypeEnum.MANUAL.getCode() == workbenchInspectInfoPo.getType())
                .collect(Collectors.toList());
        // 多个质检指标的情况下取最新质检指标结果
        Map<String, WorkbenchInspectInfoPo> pbInspectInfoMap = pbInspectInfoList.stream()
                .collect(Collectors.toMap(WorkbenchInspectInfoPo::getSessionId, Function.identity(), (a, b) -> a.getGmtModified().after(b.getGmtModified()) ? a : b));

        inspectWorkbenchDTOList.forEach(inspectWorkbenchDTO -> {
            // 获取点赞点踩信息
            Long agreeNum = null;
            Long disagreeNum = null;
            Long curMisAgreeNum = null;
            Long curMisDisagreeNum = null;
            List<InspectWorkbenchEvalPo> evalList = sessionMap.get(inspectWorkbenchDTO.getSessionId());
            if (CollectionUtils.isNotEmpty(evalList)) {
                // 统计全局维度同意和不同意的人数
                agreeNum = evalList.stream().filter(inspectWorkbenchEval -> inspectWorkbenchEval.getAgreeStatus() == InspectAgreeStatusEnum.AGREE.getCode()).count();
                disagreeNum = evalList.stream().filter(inspectWorkbenchEval -> inspectWorkbenchEval.getAgreeStatus() == InspectAgreeStatusEnum.DISAGREE.getCode()).count();
                // 统计当前登录人的同意和不同意的人数
                curMisAgreeNum = evalList.stream().filter(inspectWorkbenchEval -> inspectWorkbenchEval.getAgreeStatus() == InspectAgreeStatusEnum.AGREE.getCode() && inspectWorkbenchEval.getEvalMis().equals(UserUtils.getUser().getLogin())).count();
                curMisDisagreeNum = evalList.stream().filter(inspectWorkbenchEval -> inspectWorkbenchEval.getAgreeStatus() == InspectAgreeStatusEnum.DISAGREE.getCode() && inspectWorkbenchEval.getEvalMis().equals(UserUtils.getUser().getLogin())).count();
            }

            // 获取质检信息
            Integer inspectStatus;
            String inspector;

            Integer platformType = inspectWorkbenchDTO.getPlatformType();

            if (Objects.equals(platformType, PlatformTypeEnum.AI.getCode())) {
                inspectStatus = aidaInspectInfoMap.containsKey(inspectWorkbenchDTO.getSessionId()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode();
                inspector = aidaInspectInfoMap.containsKey(inspectWorkbenchDTO.getSessionId()) ? aidaInspectInfoMap.get(inspectWorkbenchDTO.getSessionId()).getUpdaterMis() : null;
            } else if (Objects.equals(platformType, PlatformTypeEnum.PB.getCode())) {
                inspectStatus = pbInspectInfoMap.containsKey(inspectWorkbenchDTO.getSessionId()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode();
                inspector = pbInspectInfoMap.containsKey(inspectWorkbenchDTO.getSessionId()) ? pbInspectInfoMap.get(inspectWorkbenchDTO.getSessionId()).getUpdaterMis() : null;
            } else {
                inspectStatus = YesNoEnum.NO.getCode();
                inspector = null;
            }

            // 设置结果
            inspectWorkbenchDTO.setAllStatus(InspectWorkbenchDTO.Status.createStatus(agreeNum == null ? 0 : agreeNum.intValue(), disagreeNum == null ? 0 : disagreeNum.intValue(), inspectStatus, inspector));
            inspectWorkbenchDTO.setStatus(InspectWorkbenchDTO.Status.createStatus(curMisAgreeNum == null ? 0 : curMisAgreeNum.intValue(), curMisDisagreeNum == null ? 0 : curMisDisagreeNum.intValue(), inspectStatus, inspector));
        });
    }

    private boolean hasCondition(InspectWorkbenchConditionParam condition) {
        if (condition == null) {
            return false;
        }
        if (StringUtils.isNotBlank(condition.getSessionId())) {
            return true;
        }
        if (StringUtils.isNotBlank(condition.getUserId())) {
            return true;
        }
        if (condition.getStartTime() != null && condition.getEndTime() != null) {
            return true;
        }
        if (StringUtils.isNotBlank(condition.getOrderId())) {
            return true;
        }
        if (StringUtils.isNotBlank(condition.getWorkspaceId())) {
            return true;
        }
        if (StringUtils.isNotBlank(condition.getApplicationId())) {
            return true;
        }
        return false;
    }

    private void saveSessionLog(String sessionId, String mis) {
        try {
            WorkbenchInspectVisitLogPo logPo = new WorkbenchInspectVisitLogPo();
            logPo.setType(WorkbenchLogTypeEnum.VISIT_SESSION.getCode());
            logPo.setVisitMis(mis);
            logPo.setSessionId(sessionId);
            logPo.setVisitTime(new Date());
            workbenchInspectVisitLogGeneratorService.save(logPo);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("saveSessionLog error,sessionId={},mis={}", sessionId, mis, e);
        }

    }

    private void saveQueryLog(String sessionId, String queryId, String mis) {
        try {
            WorkbenchInspectVisitLogPo logPo = new WorkbenchInspectVisitLogPo();
            logPo.setType(WorkbenchLogTypeEnum.VISIT_QUERY.getCode());
            logPo.setVisitMis(mis);
            logPo.setSessionId(sessionId);
            logPo.setQueryId(queryId);
            logPo.setVisitTime(new Date());
            workbenchInspectVisitLogGeneratorService.save(logPo);
        } catch (Exception e) {
            Cat.logError(e);
            log.error("saveQueryLog error,queryId={},mis={}", queryId, mis, e);
        }
    }

    private List<WorkspaceAppDTO> getWorkspaceAppInfo() {
        AidaResponse<List<AidaSceneAppHierarchyDTO>> aidaSceneResponse = aidaSceneRemoteService.listAllSceneApplicationsHierarchy();
        if (aidaSceneResponse.getCode() == 0 && CollectionUtils.isNotEmpty(aidaSceneResponse.getData())) {
            return convertTo(aidaSceneResponse.getData());
        }
        return new ArrayList<>();
//        return Lion.getList(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_AIDA_WORKSPACE_APPLICATION, WorkspaceAppDTO.class);
    }


    /**
     * 获取当前用户角色
     *
     * @return 当前用户的工作台角色枚举
     */
    private WorkBenchRoleEnum getCurrentUserRole() {
        String map = Lion.getString(ConfigUtil.getAppkey(), LionConstants.WORKBENCH_USER_ROLE_MAP);
        if (StringUtils.isNotBlank(map)) {
            Map<Integer, List<String>> roleMap = JSONObject.parseObject(map, new TypeReference<Map<Integer, List<String>>>() {
            });
            for (Map.Entry<Integer, List<String>> entry : roleMap.entrySet()) {
                if (entry.getValue().contains(UserUtils.getUser().getLogin())) {
                    return WorkBenchRoleEnum.getByCode(entry.getKey());
                }
            }
        }
        return WorkBenchRoleEnum.USER;
    }

    /**
     * 导出运营分析工具数据
     *
     * @param mis        用户MIS
     * @param condition  查询条件参数
     * @param sessionIds 需要导出的会话ID集合
     * @return 导出文件的URL
     */
    private String exportSession(String mis, InspectWorkbenchConditionParam condition, Collection<String> sessionIds) {
        // 1. 准备配置和基础数据
        SessionExportConfig config = prepareExportConfig(sessionIds);

        // 2. 设置基础表头
        Set<ExcelService.ColorfulHead> headList = prepareBaseHeaders();

        // 3. 处理会话数据
        List<List<String>> dataList = processSessionsData(sessionIds, condition, headList, config, mis);

        // 4. 生成文件并上传
        return generateAndUploadExcel(mis, headList, dataList);
    }

    /**
     * 准备导出配置数据
     *
     * @param sessionIds 需要导出的会话ID集合
     * @return 会话导出配置对象
     */
    private SessionExportConfig prepareExportConfig(Collection<String> sessionIds) {
        SessionExportConfig config = new SessionExportConfig();

        // 查询指标配置
        List<MetricsRelation> metricsRelationlist = Lion.getList(ConfigUtil.getAppkey(),
                LionConstants.WORKBENCH_SESSION_METRIC_RELATION, MetricsRelation.class);

        // 获取所有指标ID
        List<Long> metricIds = extractAllMetricIds(metricsRelationlist);
        config.setMetricIds(metricIds);

        // App到人工指标List的映射
        Map<String, List<Long>> appIdMetricMap = metricsRelationlist.stream()
                .collect(Collectors.toMap(
                        MetricsRelation::getSceneTypeName,
                        MetricsRelation::getManulaMetricsIdList
                ));
        config.setAppIdMetricMap(appIdMetricMap);

        // App到机器 <-> 人工指标的双向映射
        Map<String, Map<Long, Long>> appIdManualAutoMetricMap = buildMetricMappings(metricsRelationlist);
        config.setAppIdManualAutoMetricMap(appIdManualAutoMetricMap);

        // 指标ID到指标详情的映射
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(metricIds);
        Map<Long, MetricConfigPo> metricConfigMap = metricConfigList.stream()
                .collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));
        config.setMetricConfigMap(metricConfigMap);

        // 查询动作节点
        String actionConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_ACTION_NODE_INFO);
        Map<String, Map<String, String>> actionMap = JSONObject.parseObject(
                actionConfig, new TypeReference<Map<String, Map<String, String>>>() {
                });
        config.setActionMap(actionMap);

        // 查询所有评测结果
        List<WorkbenchInspectInfoPo> infoPos = workbenchInspectInfoGeneratorService.listBySessionIds(sessionIds);
        Map<String, List<WorkbenchInspectInfoPo>> sessionIdInspectInfoMap = infoPos.stream()
                .collect(Collectors.groupingBy(WorkbenchInspectInfoPo::getSessionId));
        config.setSessionIdInspectInfoMap(sessionIdInspectInfoMap);

        // 查询所有评测人
        Map<Long, Set<String>> inspectorsByMetric = infoPos.stream()
                .filter(info -> info.getType() == MetricEvalTypeEnum.MANUAL.getCode())
                .collect(Collectors.groupingBy(
                        WorkbenchInspectInfoPo::getMetricId,
                        Collectors.mapping(WorkbenchInspectInfoPo::getCreatorMis, Collectors.toSet())
                ));
        config.setInspectorsByMetric(inspectorsByMetric);

        return config;
    }

    /**
     * 提取所有指标ID
     *
     * @param metricsRelations 指标关系列表
     * @return 所有指标ID列表
     */
    private List<Long> extractAllMetricIds(List<MetricsRelation> metricsRelations) {
        return metricsRelations.stream()
                .flatMap(metricsRelation -> Stream.concat(
                        metricsRelation.getMetricsRelation().stream()
                                .map(WorkbenchMetricsMappingDTO::getAutoMetricId),
                        metricsRelation.getManulaMetricsIdList().stream()
                ))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建指标映射关系
     *
     * @param metricsRelations 指标关系列表
     * @return 应用ID到指标ID映射关系的映射
     */
    private Map<String, Map<Long, Long>> buildMetricMappings(List<MetricsRelation> metricsRelations) {
        return metricsRelations.stream()
                .collect(Collectors.toMap(
                        MetricsRelation::getSceneTypeName,
                        metricsRelation -> metricsRelation.getMetricsRelation().stream()
                                .flatMap(mapping -> Stream.of(
                                        new AbstractMap.SimpleEntry<>(mapping.getAutoMetricId(), mapping.getManualMetricId()),
                                        new AbstractMap.SimpleEntry<>(mapping.getManualMetricId(), mapping.getAutoMetricId())
                                ))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        Map.Entry::getValue
                                ))
                ));
    }

    /**
     * 准备基础表头
     *
     * @return 导出Excel的基础表头集合
     */
    private Set<ExcelService.ColorfulHead> prepareBaseHeaders() {
        return SessionDataExportEnum.listFields().stream()
                .map(head -> new ExcelService.ColorfulHead(
                        head, null, IndexedColors.LIGHT_TURQUOISE
                ))
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    /**
     * 处理所有会话数据
     *
     * @param sessionIds 需要处理的会话ID集合
     * @param condition  查询条件参数
     * @param headList   表头列表，会被修改以添加动态列
     * @param config     导出配置
     * @param mis        用户MIS
     * @return 处理后的会话数据列表
     */
    private List<List<String>> processSessionsData(
            Collection<String> sessionIds,
            InspectWorkbenchConditionParam condition,
            Set<ExcelService.ColorfulHead> headList,
            SessionExportConfig config, String mis) {

        // 根据appIdList，构造AppID AppMap 和 组户map
        List<AppDTO> appDtoS = aidaInvokeServiceProxy.listAidaAppDtoByIds(condition.getApplicationIdList());
        Map<String, AppDTO> appIdMap = appDtoS.stream().collect(Collectors.toMap(AppDTO::getId, Function.identity()));
        List<String> workspaceIdList = appDtoS.stream().map(AppDTO::getTenantId).distinct().collect(Collectors.toList());
        List<TenantDTO> tenantDTOS = aidaInvokeServiceProxy.listAidaTenantDtoByIds(workspaceIdList);
        Map<String, TenantDTO> tenantIdMap = tenantDTOS.stream().collect(Collectors.toMap(TenantDTO::getId, Function.identity()));

        List<List<String>> dataList = new ArrayList<>();

        List<String> sessionIdList = new ArrayList<>(sessionIds);
        for (int i = 0; i < sessionIdList.size(); i++) {
            String sessionId = sessionIdList.get(i);
            try {
                log.info("运营分析工具会话导出进度, 用户mis: {}, 当前进度: {}%, 正在处理sessionId: {}", mis, (i + 1) * 100 / sessionIds.size(), sessionId);
                // 获取会话基础数据
                SessionBasicInfo sessionBasicInfo = getSessionBasicInfo(sessionId, condition, appIdMap, tenantIdMap);
                if (sessionBasicInfo == null) {
                    log.warn("处理会话数据失败, 获取会话基础数据为空, sessionId={}", sessionId);
                    continue;
                }

                // 获取会话历史记录
                List<InspectWorkbenchSessionDetailDTO> historyDetails = getSessionHistoryDetails(sessionId, sessionBasicInfo.getUserInfo());
                if (CollectionUtils.isEmpty(historyDetails)) {
                    log.warn("处理会话数据失败, 获取会话历史记录为空, sessionId={}", sessionId);
                    continue;
                }

                // 添加质检相关的表头
                addInspectionHeaders(headList, sessionBasicInfo.getApplicationId(), config);

                // 处理会话的质检数据和历史记录
                processSessionHistory(dataList, sessionBasicInfo, historyDetails, headList, config);

            } catch (Exception e) {
                log.warn("处理会话数据异常, sessionId={}", sessionId, e);
                Cat.logErrorWithCategory("workbench.export.error", e);
            }
        }

        return dataList;
    }

    /**
     * 获取会话基础信息
     *
     * @param sessionId   会话ID
     * @param condition   查询条件参数
     * @param appIdMap    应用ID映射表
     * @param tenantIdMap 租户ID映射表
     * @return 会话基础信息对象，如果获取失败则返回null
     */
    private SessionBasicInfo getSessionBasicInfo(String sessionId, InspectWorkbenchConditionParam condition, Map<String, AppDTO> appIdMap, Map<String, TenantDTO> tenantIdMap) {
        // 查询aida_message表获取该Session下的所有conversionId
        List<AidaMessagesPo> aidaMessagesPos = aidaMessagesGeneratorService
                .listBySessionIdAndAppIds(sessionId, condition.getApplicationIdList());

        if (CollectionUtils.isEmpty(aidaMessagesPos)) {
            log.warn("运营分析工具数据导出, 查询aida_message表为空, sessionId: {}, appIds: {}",
                    sessionId, JSONObject.toJSONString(condition.getApplicationIdList()));
            return null;
        }

        AidaMessagesPo aidaMessagesPo = aidaMessagesPos.get(0);

        // 获取空间名称和应用名称
        String applicationId = aidaMessagesPo.getAppId();
        AppDTO app = appIdMap.get(applicationId);
        if (app == null) {
            log.warn("运营分析工具数据导出, 查询应用信息为空, sessionId: {}, appId: {}", sessionId, applicationId);
            return null;
        }

        String workspaceId = app.getTenantId();
        TenantDTO tenant = tenantIdMap.get(workspaceId);
        if (tenant == null) {
            log.warn("运营分析工具数据导出, 查询租户信息为空, sessionId: {}, workspaceId: {}", sessionId, workspaceId);
            return null;
        }

        // 查询主流程ConversionID及补充子流程
        List<String> conversionIds = getConversionIds(aidaMessagesPos, sessionId, workspaceId, applicationId,
                aidaMessagesPo.getCreatedAt()
        );

        // 远程调用Aida查询Message
//        List<MessagesDTO> messagesList;
//        if (LargeTableManagementUtil.enableNewApiWithAppId(app.getId())) {
//            messagesList = listMessageByConvIdsAndMainAppId(conversionIds);
//        } else {
//            messagesList = aidaInvokeServiceProxy.listByConversationIds(conversionIds);
//        }
        if (CollectionUtils.isEmpty(aidaMessagesPos)) {
            log.warn("运营分析工具数据导出, 查询Aida Message为空, sessionId: {}, conversionIds: {}", sessionId, JSONObject.toJSONString(conversionIds));
            return null;
        }

        // 时间顺序排序
        aidaMessagesPos.sort(Comparator.comparing(AidaMessagesPo::getCreatedAt));

        // 获取用户信息
        UserInfo userInfo = getUserInfoByAidaMessages(aidaMessagesPos);

        SessionBasicInfo basicInfo = new SessionBasicInfo();
        basicInfo.setSessionId(sessionId);
        basicInfo.setApplicationId(applicationId);
        basicInfo.setApplicationName(app.getName());
        basicInfo.setWorkspaceId(workspaceId);
        basicInfo.setWorkspaceName(tenant.getName());
        basicInfo.setMessagesList(aidaMessagesPos);
        basicInfo.setUserInfo(userInfo);
        basicInfo.setExtraInfo(aidaMessagesPos.get(0).getExtraInfo());

        return basicInfo;
    }

    /**
     * 获取会话的转换ID列表
     *
     * @param aidaMessagesPos AIDA消息列表
     * @param sessionId       会话ID
     * @param workspaceId     工作空间ID
     * @param applicationId   应用ID
     * @param createdAt       创建时间
     * @return 转换ID列表
     */
    private List<String> getConversionIds(List<AidaMessagesPo> aidaMessagesPos, String sessionId, String workspaceId, String applicationId, Date createdAt) {
        // 主流程ID
        List<String> conversionIds = aidaMessagesPos.stream()
                .map(AidaMessagesPo::getConversationId)
                .distinct()
                .collect(Collectors.toList());

        // 补充子流程, 当前会话前后1天查询，缩小范围，加快速度 TODO 2025-02-27 在评测收集一定时间的子流程数据后可以去掉该方法
        conversionIds.addAll(getSubApplicationConversationId(sessionId, workspaceId, applicationId, DateUtil.addDays(createdAt, -1), DateUtil.addDays(createdAt, 1)));

        return conversionIds;
    }

    /**
     * 获取会话历史详情
     *
     * @param sessionId 会话ID
     * @param userInfo  用户信息
     * @return 会话历史详情列表，如果获取失败则返回null
     */
    private List<InspectWorkbenchSessionDetailDTO> getSessionHistoryDetails(String sessionId, UserInfo userInfo) {
        try {
            // 根据sessionId查一个message判断是channel
            List<AidaMessagesPo> aidaMessagesList = aidaMessagesGeneratorService.listBySessionId(sessionId);
            if (CollectionUtils.isNotEmpty(aidaMessagesList)) {
                AidaMessagesPo messagesPo = aidaMessagesList.get(0);
                String channelCode = WorkbenchDataProcessor.parseChannelCode(messagesPo.getExtraInfo());
                if (ChannelEnum.IVR.getCode().equals(channelCode)) {
                    return getIvrSessionHistoryDetails(sessionId);
                }
            }
            // Online在线会话逻辑
            return getFilteredSessionHistoryDetails(sessionId, userInfo);
        } catch (Exception e) {
            log.warn("获取会话历史详情异常, sessionId={}", sessionId, e);
            Cat.logErrorWithCategory("workbench.export.history.error", e);
            return null;
        }
    }

    /**
     * 添加质检相关的表头
     *
     * @param headList      表头列表，会被修改以添加质检相关列
     * @param applicationId 应用ID
     * @param config        导出配置
     */
    private void addInspectionHeaders(Set<ExcelService.ColorfulHead> headList, String applicationId, SessionExportConfig config) {
        Map<String, List<Long>> appIdMetricMap = config.getAppIdMetricMap();
        Map<String, Map<Long, Long>> appIdManualAutoMetricMap = config.getAppIdManualAutoMetricMap();
        Map<Long, MetricConfigPo> metricConfigMap = config.getMetricConfigMap();
        Map<Long, Set<String>> inspectorsByMetric = config.getInspectorsByMetric();

        List<List<IndexedColors>> headColors = Arrays.asList(
                Arrays.asList(IndexedColors.BRIGHT_GREEN1, IndexedColors.LIGHT_GREEN),
                Arrays.asList(IndexedColors.SKY_BLUE, IndexedColors.PALE_BLUE)
        );

        int colorIndex = 0;

        for (Long metricId : appIdMetricMap.getOrDefault(applicationId, Collections.emptyList())) {
            List<IndexedColors> indexedColors = headColors.get(colorIndex);
            int currentColor = 0;
            MetricConfigPo configPo = metricConfigMap.get(metricId);
            boolean isAdd = false;

            // 添加机器结果列
            if (appIdManualAutoMetricMap.containsKey(applicationId) &&
                    appIdManualAutoMetricMap.get(applicationId).containsKey(metricId)) {

                headList.add(new ExcelService.ColorfulHead(
                        String.format(ExportConstants.METRIC_MACHINE_FORMAT, configPo.getName()),
                        null,
                        indexedColors.get(currentColor)
                ));

                currentColor = (currentColor + 1) % indexedColors.size();
                isAdd = true;
            }

            // 添加人工评测列
            if (inspectorsByMetric.containsKey(metricId)) {
                for (String inspector : inspectorsByMetric.get(metricId).stream()
                        .sorted().collect(Collectors.toList())) {

                    headList.add(new ExcelService.ColorfulHead(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, configPo.getName(), inspector, ExportConstants.EVALUATION_RESULT),
                            null,
                            indexedColors.get(currentColor)
                    ));

                    headList.add(new ExcelService.ColorfulHead(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, configPo.getName(), inspector, ExportConstants.EVALUATION_TIME),
                            null,
                            indexedColors.get(currentColor)
                    ));

                    headList.add(new ExcelService.ColorfulHead(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, configPo.getName(), inspector, ExportConstants.EVALUATION_NOTE),
                            null,
                            indexedColors.get(currentColor)
                    ));

                    currentColor = (currentColor + 1) % indexedColors.size();
                }
                isAdd = true;
            }

            if (isAdd) {
                colorIndex = (colorIndex + 1) % headColors.size();
            }
        }
    }

    /**
     * 处理会话历史数据
     *
     * @param dataList         数据列表，会被修改以添加处理后的会话历史数据
     * @param sessionBasicInfo 会话基础信息
     * @param historyDetails   历史详情列表
     * @param headList         表头列表
     * @param config           导出配置
     */
    private void processSessionHistory(List<List<String>> dataList,
                                       SessionBasicInfo sessionBasicInfo,
                                       List<InspectWorkbenchSessionDetailDTO> historyDetails,
                                       Set<ExcelService.ColorfulHead> headList,
                                       SessionExportConfig config) {

        Map<String, String> resultMap = new HashMap<>();
        List<Map<String, String>> history = new ArrayList<>();

        // 填充质检数据
        fillInspectionData(resultMap, sessionBasicInfo.getApplicationId(), sessionBasicInfo.getSessionId(), config);

        // 填充会话历史
        int turn = 0;
        for (InspectWorkbenchSessionDetailDTO historyDetail : historyDetails) {
            Map<String, String> curMessage = new HashMap<>();
            RoleEnum role = RoleEnum.getByCode(historyDetail.getSenderType());
            curMessage.put("role", role == null ? "" : role.getDescription());
            curMessage.put("content", historyDetail.getMessage());

            if (!historyDetail.getIsLlm()) {
                history.add(curMessage);
                continue;
            }
            turn++;

            // 填充消息详情数据
            fillMessageDetailData(resultMap, sessionBasicInfo, historyDetail, history, turn, config);

            // 将所有数据添加到结果列表
            ArrayList<String> row = new ArrayList<>();
            for (ExcelService.ColorfulHead col : headList) {
                row.add(resultMap.getOrDefault(col.getHeadName(), ""));
            }
            dataList.add(row);
            history.add(curMessage);
        }
        if (turn == 0) {
            log.warn("运营分析工具数据导出, 当前会话无LLM消息, sessionId: {}", sessionBasicInfo.getSessionId());
        }
    }

    /**
     * 填充质检数据
     *
     * @param resultMap     结果映射，会被修改以添加质检数据
     * @param applicationId 应用ID
     * @param sessionId     会话ID
     * @param config        导出配置
     */
    private void fillInspectionData(
            Map<String, String> resultMap,
            String applicationId,
            String sessionId,
            SessionExportConfig config) {

        Map<String, List<WorkbenchInspectInfoPo>> sessionIdInspectInfoMap = config.getSessionIdInspectInfoMap();
        Map<String, Map<Long, Long>> appIdManualAutoMetricMap = config.getAppIdManualAutoMetricMap();
        Map<Long, MetricConfigPo> metricConfigMap = config.getMetricConfigMap();

        // 默认未质检
        resultMap.put(SessionDataExportEnum.STATUS.getField(), ExportConstants.STATUS_NOT_EVALUATED);

        if (sessionIdInspectInfoMap.containsKey(sessionId)) {
            List<WorkbenchInspectInfoPo> infos = sessionIdInspectInfoMap.get(sessionId);
            for (WorkbenchInspectInfoPo info : infos) {
                if (info.getType() == MetricEvalTypeEnum.AUTO.getCode()) {
                    // 处理机器质检结果
                    if (appIdManualAutoMetricMap.containsKey(applicationId) &&
                            appIdManualAutoMetricMap.get(applicationId).containsKey(info.getMetricId())) {

                        Long manualId = appIdManualAutoMetricMap.get(applicationId).get(info.getMetricId());
                        MetricConfigPo config1 = metricConfigMap.get(manualId);
                        resultMap.put(
                                String.format(ExportConstants.METRIC_MACHINE_FORMAT, config1.getName()),
                                info.getMetricResult()
                        );
                    }
                } else if (info.getType() == MetricEvalTypeEnum.MANUAL.getCode()) {
                    // 处理人工质检结果
                    resultMap.put(SessionDataExportEnum.STATUS.getField(), ExportConstants.STATUS_EVALUATED);
                    MetricConfigPo config1 = metricConfigMap.get(info.getMetricId());
                    resultMap.put(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, config1.getName(), info.getCreatorMis(), ExportConstants.EVALUATION_RESULT),
                            info.getMetricResult()
                    );
                    resultMap.put(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, config1.getName(), info.getCreatorMis(), ExportConstants.EVALUATION_TIME),
                            DateUtil.format(info.getGmtModified(), null)
                    );
                    resultMap.put(
                            String.format(ExportConstants.METRIC_MANUAL_FORMAT, config1.getName(), info.getCreatorMis(), ExportConstants.EVALUATION_NOTE),
                            info.getNote()
                    );
                }
            }
        }
    }

    /**
     * 填充消息详情数据
     *
     * @param resultMap        结果映射，会被修改以添加消息详情数据
     * @param sessionBasicInfo 会话基础信息
     * @param historyDetail    历史详情
     * @param history          历史记录列表
     * @param turn             当前轮次
     * @param config           导出配置
     */
    private void fillMessageDetailData(Map<String, String> resultMap, SessionBasicInfo sessionBasicInfo,
                                       InspectWorkbenchSessionDetailDTO historyDetail, List<Map<String, String>> history, int turn,
                                       SessionExportConfig config) {

        Map<String, Map<String, String>> actionMap = config.getActionMap();

        // 获取QueryDetail
        InspectWorkbenchDetailParam param = new InspectWorkbenchDetailParam();
        param.setApplicationId(sessionBasicInfo.getApplicationId());
        param.setOriginMessage(historyDetail.getOriginMessage());
        param.setPlatform(PlatformTypeEnum.AI.getCode());
        InspectWorkbenchQueryDetailDTO queryDetail = buildAidaQueryDetailDTO(param, sessionBasicInfo.getMessagesList(), null);

        JSONObject parsed = JSONObject.parseObject(sessionBasicInfo.getExtraInfo());

        // 基本填充数据
        resultMap.put(SessionDataExportEnum.SESSION_ID.getField(), sessionBasicInfo.getSessionId());
        resultMap.put(SessionDataExportEnum.WORKSPACE_NAME.getField(), sessionBasicInfo.getWorkspaceName());
        resultMap.put(SessionDataExportEnum.APPLICATION_NAME.getField(), sessionBasicInfo.getApplicationName());
        resultMap.put(SessionDataExportEnum.CREATE_TIME.getField(), DateUtil.format(historyDetail.getTime(), null));
        resultMap.put(SessionDataExportEnum.USER_ID.getField(), parsed.getString("userId"));
        resultMap.put(SessionDataExportEnum.ORDER_ID.getField(), parsed.getString("orderId"));
        resultMap.put(SessionDataExportEnum.TURNS.getField(), String.valueOf(turn));

        // 历史记录和信号
        resultMap.put(SessionDataExportEnum.HISTORY.getField(), JSONObject.toJSONString(history, SerializerFeature.PrettyFormat));


        if (queryDetail == null || queryDetail.getDetail() == null || CollectionUtils.isEmpty(queryDetail.getDetail().getBasicInfo())) {
            return;
        }
        // 输入输出内容
        resultMap.put(SessionDataExportEnum.INPUT_CONTENT.getField(),
                queryDetail.getDetail().getBasicInfo().stream()
                        .filter(x -> ExportConstants.INPUT_DISPLAY_NAME.equals(x.getDisplayName()))
                        .findFirst()
                        .orElse(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo(null, ""))
                        .getDisplayContent());

        resultMap.put(SessionDataExportEnum.OUTPUT_CONTENT.getField(),
                queryDetail.getDetail().getBasicInfo().stream()
                        .filter(x -> ExportConstants.OUTPUT_DISPLAY_NAME.equals(x.getDisplayName()))
                        .findFirst()
                        .orElse(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo(null, ""))
                        .getDisplayContent());


        if (CollectionUtils.isNotEmpty(queryDetail.getSignalList())) {
            resultMap.put(SessionDataExportEnum.SIGNAL.getField(),
                    JSONObject.toJSONString(queryDetail.getSignalList().stream()
                            .map(signal -> Collections.singletonMap(signal.getSignalName(), signal.getSignalValue()))
                            .collect(Collectors.toList()), SerializerFeature.PrettyFormat));
        }

        // 调用信息
        resultMap.put(SessionDataExportEnum.CALLS.getField(),
                JSONObject.toJSONString(queryDetail.getDetail().getAidaTraceList().stream()
                        .filter(trace -> {
                            if (actionMap.containsKey(trace.getApplicationId())) {
                                return actionMap.get(trace.getApplicationId()).containsKey(trace.getNodeId());
                            }
                            return false;
                        })
                        .map(trace -> Collections.singletonMap(
                                StringUtils.isBlank(actionMap.get(trace.getApplicationId()).get(trace.getNodeId())) ?
                                        trace.getNodeName() :
                                        actionMap.get(trace.getApplicationId()).get(trace.getNodeId()),
                                trace.getOutput() == null ? "" : trace.getOutput()
                        ))
                        .collect(Collectors.toList())));
    }

    /**
     * 生成并上传Excel文件
     *
     * @param mis      用户MIS
     * @param headList 表头列表
     * @param dataList 数据列表
     * @return 上传后的文件URL
     */
    private String generateAndUploadExcel(
            String mis,
            Set<ExcelService.ColorfulHead> headList,
            List<List<String>> dataList) {

        String filename = String.format("在线会话分析-数据导出-%s.xlsx",
                DateUtil.format(new Date(), "yyyy_MM_dd_HH_mm_ss"));

        String s3Filename = String.format("%s-%d.xlsx", filename, System.currentTimeMillis());

        String url = excelService.generateSingletonColorfulHeadExcelAndUpload(
                new ArrayList<>(headList),
                dataList,
                "运营分析数据",
                s3Filename,
                filename,
                "xlsx",
                mis
        );

        log.info("运营分析工具数据导出, 生成文件并上传成功, mis: {}, url: {}", mis, url);

        return url;
    }

    /**
     * 发送会话导出大象消息
     *
     * @param mis 用户Mis
     * @param url 导出文件URL
     */
    private void sendSessionExportDxMessage(String mis, String url) {
        // 发送大象消息
        String message = String.format("您所选择的会话数据已导出完成，[点击查看详情|%s]", url);
        try {
            dxPushTextService.pushTextByMisName(message, mis);
            log.info("运营分析工具数据导出, 发送大象消息成功, mis: {}, message: {}", mis, message);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("运营分析工具数据导出, 发送大象消息失败, mis: {}, message: {}", mis, message, e);
        }
    }

    /**
     * 发送会话导出失败大象消息
     *
     * @param mis 用户Mis
     */
    private void sendSessionExportFailureNotification(String mis) {
        // 发送大象消息
        String message = "您所选择的会话数据导出过程中出现异常，请重试导出";
        try {
            dxPushTextService.pushTextByMisName(message, mis);
            log.info("发送导出失败大象消息成功, mis: {}, message: {}", mis, message);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
            log.error("发送导出失败大象消息失败, mis: {}, message: {}", mis, message, e);
        }
    }

    /**
     * 将输入字符串按指定的分隔符分割，并过滤掉空的字符串部分
     *
     * @param input 原始字符串
     * @return 包含分割后有效字符串的列表
     */
    public static List<String> splitString(String input) {
        String[] parts = input.split(SESSION_ID_LIST_SPLIT);
        List<String> stringList = new ArrayList<>();

        for (String part : parts) {
            if (StringUtils.isNotBlank(part)) {
                stringList.add(part);
            }
        }

        return stringList;
    }

    /**
     * 会话基础信息类
     */
    @Data
    private static class SessionBasicInfo {
        private String sessionId;
        private String applicationId;
        private String applicationName;
        private String workspaceId;
        private String workspaceName;
        /**
         * session所有消息
         */
        private List<AidaMessagesPo> messagesList;
        private UserInfo userInfo;
        private String extraInfo;
    }


    @Data
    private static class SlotInfo {
        private String slotName;
        private String slotValue;
    }

    @Data
    private static class SceneRelation {
        private String sceneTypeName;
        private String platformWorkspace;
        private List<List<String>> keywordsList;
    }

    @Data
    private static class MetricsRelation {
        /**
         * 场景类型名称
         */
        private String sceneTypeName;
        /**
         * 人工质检指标ID
         */
        private List<Long> manulaMetricsIdList;
        /**
         * key是机器质检指标ID，value是人工质检指标ID
         */
        private List<WorkbenchMetricsMappingDTO> metricsRelation;

    }

    @Data
    private static class UserInfo implements Serializable {

        private String userId;

        private String userType;
    }

    @Data
    private static class AidaSignalInfo implements Serializable {

        private String nodeId;

        private String signalKey;
    }

    /**
     * 导出会话数据配置类
     */
    @Data
    private static class SessionExportConfig {
        // 指标相关配置
        /**
         * 所有指标id（人工+自动）
         */
        private List<Long> metricIds;
        /**
         * 应用id -> 仅人工指标id列表
         */
        private Map<String, List<Long>> appIdMetricMap;
        /**
         * 应用id -> 人工自动、自动人工映射
         */
        private Map<String, Map<Long, Long>> appIdManualAutoMetricMap;
        /**
         * 指标id -> 指标详情
         */
        private Map<Long, MetricConfigPo> metricConfigMap;

        // 其他配置
        /**
         * 应用 -> {节点id, ""}的映射
         */
        private Map<String, Map<String, String>> actionMap;
        /**
         * sessionId -> 所有评测结果列表的映射
         */
        private Map<String, List<WorkbenchInspectInfoPo>> sessionIdInspectInfoMap;
        /**
         * 指标id -> 所有评测人列表
         */
        private Map<Long, Set<String>> inspectorsByMetric;
    }

    // 常量定义
    private static final class ExportConstants {
        // 表头相关常量
        static final String MACHINE_RESULT = "机器结果";
        static final String EVALUATION_RESULT = "评测结果";
        static final String EVALUATION_TIME = "评测时间";
        static final String EVALUATION_NOTE = "评测备注";

        // 格式模板
        static final String METRIC_MACHINE_FORMAT = "%s-" + MACHINE_RESULT;
        static final String METRIC_MANUAL_FORMAT = "%s-%s-%s";

        // 状态
        static final String STATUS_EVALUATED = "已质检";
        static final String STATUS_NOT_EVALUATED = "未质检";

        // 查询相关
        static final String INPUT_DISPLAY_NAME = "输入";
        static final String OUTPUT_DISPLAY_NAME = "输出";
    }


    private List<InspectWorkbenchQueryDetailDTO.Signal> processSignal(List<InspectWorkbenchQueryDetailDTO.Signal> signalSortList,
                                                                      List<InspectWorkbenchQueryDetailDTO.Signal> signalSortListConfig) {
        // 将 signalSortList 转化为 Map，方便后续查找
        Map<String, InspectWorkbenchQueryDetailDTO.Signal> signalSortMap = signalSortList.stream()
                .collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Signal::getSignalKey, Function.identity(), (a, b) -> a));

        // 处理排序
        List<InspectWorkbenchQueryDetailDTO.Signal> sortedSignalSortList = signalSortListConfig.stream()
                .map(signalSortConfig -> processSignalSort(signalSortConfig, signalSortMap))
                .filter(Objects::nonNull) // 过滤掉为 null 的值
                .collect(Collectors.toList());

        // 添加剩余的 signalSort
        sortedSignalSortList.addAll(signalSortMap.values());
        return sortedSignalSortList;
    }

    /***
     * 处理一级排序
     * @param signalSortConfig
     * @param signalSortMap
     * @return
     */
    private InspectWorkbenchQueryDetailDTO.Signal processSignalSort(InspectWorkbenchQueryDetailDTO.Signal signalSortConfig, Map<String, InspectWorkbenchQueryDetailDTO.Signal> signalSortMap) {
        InspectWorkbenchQueryDetailDTO.Signal signalSort = signalSortMap.get(signalSortConfig.getSignalKey());
        if (signalSort == null) {
            return null;
        }

        // 移除处理过的 SignalSort
        signalSortMap.remove(signalSortConfig.getSignalKey());
        return signalSort;
    }

    private List<InspectWorkbenchQueryDetailDTO.Signal> getOldValueSort(InspectWorkbenchDetailParam param) {
        InspectWorkbenchQueryDetailDTO lastQueryDetail = getOldQueryDetailDTO(param);
        // 如果查询的应用与返回的不是同一个应用则返回空数组 没有比较的价值
        boolean present = Optional.ofNullable(lastQueryDetail)
                .map(InspectWorkbenchQueryDetailDTO::getMessageLevelAidaAppInfo)  // 获取 MessageLevelAidaAppInfo
                .map(MessageLevelAidaAppInfoDTO::getApplicationId)  // 获取 ApplicationId
                .filter(applicationId -> StringUtils.equals(applicationId, param.getApplicationId()))  // 比较 ApplicationId
                .isPresent();
        if (!present) {
            return new ArrayList<>();
        }

        return Optional.of(lastQueryDetail)
                .map(InspectWorkbenchQueryDetailDTO::getAllSignal)
                .map(InspectWorkbenchQueryDetailDTO.AidaSignal::getSignalAfterList)
                .orElseGet(ArrayList::new);
    }

    private InspectWorkbenchQueryDetailDTO getOldQueryDetailDTO(InspectWorkbenchDetailParam param) {
        List<InspectWorkbenchSessionDetailDTO> sessionDetailList = sessionDetail(param.getSessionId(), param.getPlatform(), param.getLlmSessionIdList());
        // 找到所有大模型回复数据
        sessionDetailList = sessionDetailList.stream().filter(sessionDetail -> sessionDetail.getIsLlm() != null && sessionDetail.getIsLlm()).collect(Collectors.toList());
        if (Objects.equals(param.getPlatform(), PlatformTypeEnum.AI.getCode())) {
            // 当前AI平台只有工作流可以
            sessionDetailList = sessionDetailList.stream()
                    .filter(sessionDetail -> (null != sessionDetail.getMessageLevelAidaAppInfo()) && (Objects.equals(sessionDetail.getMessageLevelAidaAppInfo().getApplicationType(), AidaApplicationTypeEnum.RULE.getCode())))
                    .collect(Collectors.toList());
        }
        // 找到上一个大模型消息
        if (CollectionUtils.isNotEmpty(sessionDetailList)) {
            InspectWorkbenchSessionDetailDTO lastLlmSession = null;
            for (InspectWorkbenchSessionDetailDTO sessionDetail : sessionDetailList) {
                if (sessionDetail.getMessageId().equals(param.getMessageId())) {
                    break;
                }
                lastLlmSession = sessionDetail;
            }
            if (lastLlmSession != null) {
                InspectWorkbenchDetailParam lastLlmParam = buildInspectWorkbenchDetailParam(param, lastLlmSession);
                return queryDetail(lastLlmParam);
            }
        }
        return null;
    }

    private void compareOldValueSort(List<InspectWorkbenchQueryDetailDTO.Signal> oldValueList,
                                     List<InspectWorkbenchQueryDetailDTO.Signal> signalList) {
        if (CollectionUtils.isEmpty(oldValueList) || CollectionUtils.isEmpty(signalList)) {
            return;
        }

        // 将旧值列表转换为 Map，便于快速查找
        Map<String, InspectWorkbenchQueryDetailDTO.Signal> oldValueMap = oldValueList.stream()
                .collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Signal::getSignalName, Function.identity()));

        for (InspectWorkbenchQueryDetailDTO.Signal signal : signalList) {
            try {
                InspectWorkbenchQueryDetailDTO.Signal oldSignal = oldValueMap.get(signal.getSignalName());
                if (oldSignal == null || StringUtils.isEmpty(signal.getSignalValue()) || StringUtils.isEmpty(oldSignal.getSignalValue())) {
                    continue;
                }
                if (!StringUtils.equals(StringUtils.defaultIfBlank(oldSignal.getSignalValue(), ""), StringUtils.defaultIfBlank(signal.getSignalValue(), ""))) {
                    signal.setOldValue(oldSignal.getSignalValue());
                    signal.setIsChange(true);
                }
            } catch (Exception e) {
                log.error("InspectWorkbench compareOldValueSort error,oldValueList:{},signalList:{}", JSON.toJSONString(oldValueList), JSON.toJSONString(signalList), e);
            }
        }
    }

    // 将 OldTreeNode 转换为 NewTreeNode
    public InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree filterCanDebug(InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree aidaLlmTree, List<String> nodeDebugWhiteList, List<String> nodeDebugBlackList) {
        // 叶子节点 并且不在黑名单内
        if (CollectionUtils.isEmpty(aidaLlmTree.getChildList())) {
            if (!nodeDebugBlackList.contains(aidaLlmTree.getAppId())) {
                aidaLlmTree.setCanDebug(true);
            }
            return aidaLlmTree;
        }

        // 模型节点在白名单内可以调试
        if (nodeDebugWhiteList.contains(aidaLlmTree.getAppId())) {
            aidaLlmTree.setCanDebug(true);
        }

        // 递归转换每个子节点
        List<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> newChildren = aidaLlmTree.getChildList().stream()
                .map(child -> filterCanDebug(child, nodeDebugWhiteList, nodeDebugBlackList))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        aidaLlmTree.setChildList(newChildren);
        // 创建新节点，并返回
        return aidaLlmTree;
    }

    public InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree convertAppTree(AidaAppTreeDTO oldNode, Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> stringAidaTraceMap) {
        if (!stringAidaTraceMap.containsKey(oldNode.getNodeId())) {
            return null;
        }
        InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree aidaLlmTree = buildApp(oldNode.getNodeId(), oldNode.getNodeName(), oldNode.getAppId(), oldNode.getAppName(), oldNode.getAidaAppVersionId());
        InspectWorkbenchQueryDetailDTO.Detail.AidaTrace aidaTrace = stringAidaTraceMap.get(oldNode.getNodeId());

        // 入参
        String inputStr = Optional.ofNullable(aidaTrace)
                .map(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getInput)
                .orElse(null);
        Object input;
        if (StringUtils.isNotBlank(inputStr) && JSONValidator.from(inputStr).getType() == JSONValidator.Type.Object) {
            input = JSON.parseObject(inputStr);
        } else {
            Map<String, Object> rs = new HashMap<>();
            rs.put("input", inputStr);
            input = rs;
        }
        List<InspectWorkbenchQueryLlmTreeDTO.FieldMapping> inputFieldMappingList = convertOutputFieldMappings(oldNode.getInputFieldMapping());
        aidaLlmTree.setInputFieldMapping(inputFieldMappingList);
        aidaLlmTree.setInput(input);

        // 出参
        String outputStr = Optional.ofNullable(aidaTrace)
                .map(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getOutput)
                .orElse(null);
        Object output;
        if (StringUtils.isNotBlank(outputStr) && JSONValidator.from(outputStr).getType() == JSONValidator.Type.Object) {
            output = JSON.parseObject(outputStr);
        } else {
            Map<String, Object> rs = new HashMap<>();
            rs.put("output", outputStr);
            output = rs;
        }
        List<InspectWorkbenchQueryLlmTreeDTO.FieldMapping> outputFieldMappingList = convertOutputFieldMappings(oldNode.getOutputFieldMapping());
        aidaLlmTree.setOutputFieldMapping(outputFieldMappingList);
        aidaLlmTree.setOutput(output);
        aidaLlmTree.setModel(oldNode.getModel());

        if (CollectionUtils.isEmpty(oldNode.getChildList())) {
            return aidaLlmTree;
        }

        // 递归转换每个子节点
        List<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> newChildren = oldNode.getChildList().stream()
                .map(child -> convertAppTree(child, stringAidaTraceMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        aidaLlmTree.setChildList(newChildren);
        // 创建新节点，并返回
        return aidaLlmTree;
    }

    private List<InspectWorkbenchQueryLlmTreeDTO.FieldMapping> convertOutputFieldMappings(List<AidaAppTreeDTO.FieldMapping> fieldMappingList) {
        return CollectionUtils.emptyIfNull(fieldMappingList).stream()
                .map(fieldMapping -> new InspectWorkbenchQueryLlmTreeDTO.FieldMapping(fieldMapping.getFieldKey(), fieldMapping.getFieldName()))
                .collect(Collectors.toList());
    }

    private InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree buildApp(String nodeId, String nodeName, String appId, String appName, String aidaAppVersionId) {
        InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree aidaLlmTree = new InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree();
        aidaLlmTree.setAppId(appId);
        aidaLlmTree.setAppName(appName);
        aidaLlmTree.setAidaAppVersionId(aidaAppVersionId);
        aidaLlmTree.setCanDebug(false);
        aidaLlmTree.setNodeId(nodeId);
        aidaLlmTree.setNodeName(nodeName);
        return aidaLlmTree;
    }

    /***
     * 重构config
     * @param nodeId
     * @param sourceConfig
     * @param targetConfig
     * @param type
     * @return
     */
    private static String reBuildConfig(String nodeId, String sourceConfig, String targetConfig, CustomConfigTypeEnum type) {
        String rs = null;
        switch (type) {
            case SIGNAL_NODE_SORT: {
                Map<String, List<InspectWorkbenchQueryDetailDTO.Signal>> nodeSortConfigMap = new HashMap<>();
                if (StringUtils.isNotBlank(sourceConfig)) {
                    nodeSortConfigMap = JSON.parseObject(sourceConfig, new TypeReference<Map<String, List<InspectWorkbenchQueryDetailDTO.Signal>>>() {
                    });
                }
                List<InspectWorkbenchQueryDetailDTO.Signal> processedSignalSortList = reBuildSignalSortList(targetConfig);
                nodeSortConfigMap.put(nodeId, processedSignalSortList);
                rs = JSON.toJSONString(nodeSortConfigMap);
                break;
            }
            case SIGNAL_ALL_SORT: {
                List<InspectWorkbenchQueryDetailDTO.Signal> processedSignalSortList = reBuildSignalSortList(targetConfig);
                rs = JSON.toJSONString(processedSignalSortList);
                break;
            }
            default: {
                break;
            }
        }
        return rs;
    }

    /***
     *  清空不需要的字段值
     * @param targetConfig
     * @return
     */
    private static List<InspectWorkbenchQueryDetailDTO.Signal> reBuildSignalSortList(String targetConfig) {
        return JSON.parseArray(targetConfig, InspectWorkbenchQueryDetailDTO.Signal.class)
                .stream()
                .peek(signalSort -> {
                    // 清空不需要的字段
                    signalSort.setSignalValue(null);
                    signalSort.setOldValue(null);
                    signalSort.setIsChange(null);
                })
                .collect(Collectors.toList());
    }

    private static GptRequestDTO buildGptRequestDTO(WorkbenchLlmDebugParam param) {
        JSONObject input = Optional.ofNullable(param.getInput())
                .map(JSON::toJSONString)
                .map(JSON::parseObject)
                .orElse(null);
        CommonUtils.checkEval(input != null, "input 必传");

        GptRequestDTO gptRequest = new GptRequestDTO();
        gptRequest.setConversionId(null);
        gptRequest.setUser(UserUtils.getUser().getLogin());
        gptRequest.setAidaAppId(param.getAppId());
        gptRequest.setModelConfigVersionId(param.getAidaAppVersionId());
        gptRequest.setInputContent(input.getString("query"));
        if (ObjectUtils.anyNotNull(input.get("inputs"))) {
            Map<String, String> dataParams = JSON.parseObject(input.get("inputs").toString(), new TypeReference<Map<String, String>>() {
            });
            gptRequest.setDataParams(dataParams);
        }
        Map<String, String> businessParam = new HashMap<>();
        if (StringUtils.isNotBlank(input.getString(TemplateFieldEnum.EXTRA_INFO.getCode()))) {
            businessParam.put(TemplateFieldEnum.EXTRAINFO.getCode(), input.getString(TemplateFieldEnum.EXTRA_INFO.getCode()));
        }
        if (StringUtils.isNotBlank(input.getString(TemplateFieldEnum.EXTRAINFO.getCode()))) {
            businessParam.put(TemplateFieldEnum.EXTRAINFO.getCode(), input.getString(TemplateFieldEnum.EXTRAINFO.getCode()));
        }
        gptRequest.setBusinessParam(JSON.toJSONString(businessParam));

        gptRequest.setModel(param.getAidaAppVersionId());
        gptRequest.setIsInner(false);
        // Histories
        List<ChatGptHttpRequest.GptMessage> messageList = JSON.parseArray(input.getString(TemplateFieldEnum.HISTORIES.getCode()), ChatGptHttpRequest.GptMessage.class);
        gptRequest.setMessageList(messageList);
        return gptRequest;
    }

    private InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree convertAidaLlmTree(List<AidaAppTreeDTO> appTreeDTOList, List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> aidaTraceList) {
        // 获取调试节点白名单
        List<String> nodeDebugWhiteList = getLionWorkFlowDebugConfig(LionConstants.AIDA_WORK_FLOW_DEBUG_WHITE_LIST_NODE);

        // 获取调试节点白名单
        List<String> nodeDebugBlackList = getLionWorkFlowDebugConfig(LionConstants.AIDA_WORK_FLOW_DEBUG_BLACK_LIST_NODE);

        AidaAppTreeDTO aidaAppTreeDTOFirst = appTreeDTOList.get(0);

        if (aidaAppTreeDTOFirst == null || CollectionUtils.isEmpty(aidaAppTreeDTOFirst.getChildList())) {
            return new InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree();
        }
        // 获取大模型节点日志
        Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> aidaTraceLlmMap = aidaTraceList.stream()
                .filter(aidaTrace -> StringUtils.equals(aidaTrace.getNodeType(), LLM_NODE_TYPE))
                .collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getNodeId, Function.identity(), (first, after) -> first));

        // 转换为调试参数
        List<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> aidaLlmTreeChildList = aidaAppTreeDTOFirst.getChildList().stream()
                .map(aidaAppTreeDTO -> convertAppTree(aidaAppTreeDTO, aidaTraceLlmMap))
                .filter(Objects::nonNull)
                .map(aidaLlmTree -> filterCanDebug(aidaLlmTree, nodeDebugWhiteList, nodeDebugBlackList))
                .collect(Collectors.toList());

        // 一级agent节点
        InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree aidaLlmTree = buildApp(aidaAppTreeDTOFirst.getNodeId(), aidaAppTreeDTOFirst.getNodeName(),
                aidaAppTreeDTOFirst.getAppId(), aidaAppTreeDTOFirst.getAppName(),
                aidaAppTreeDTOFirst.getAidaAppVersionId());

        if (nodeDebugWhiteList.contains(aidaLlmTree.getAppId())) {
            aidaLlmTree.setCanDebug(true);
        }

        aidaLlmTree.setChildList(aidaLlmTreeChildList);

        // 取第一个开始节点
        InspectWorkbenchQueryDetailDTO.Detail.AidaTrace aidaTraceFirst = aidaTraceList.stream()
                .filter(log -> START_NODE_TYPE.equals(log.getNodeType()))
                .findFirst()
                .orElse(null);
        // 入参
        String inputStr = Optional.ofNullable(aidaTraceFirst)
                .map(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getInput)
                .orElse(null);
        Object input;
        if (StringUtils.isNotBlank(inputStr) && JSONValidator.from(inputStr).getType() == JSONValidator.Type.Object) {
            input = JSON.parseObject(inputStr);
        } else {
            Map<String, Object> rs = new HashMap<>();
            rs.put("input", inputStr);
            input = rs;
        }
        List<InspectWorkbenchQueryLlmTreeDTO.FieldMapping> inputFieldMappingList = convertOutputFieldMappings(aidaAppTreeDTOFirst.getInputFieldMapping());
        aidaLlmTree.setInputFieldMapping(inputFieldMappingList);
        aidaLlmTree.setInput(input);
        // 出参
        InspectWorkbenchQueryDetailDTO.Detail.AidaTrace aidaTraceLast = aidaTraceList.stream()
                .filter(log -> REPLY_NODE_TYPE.equals(log.getNodeType()))
                .reduce((first, second) -> second)
                .orElse(null);
        String outputStr = Optional.ofNullable(aidaTraceLast)
                .map(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getOutput)
                .orElse(null);

        Object output;
        if (StringUtils.isNotBlank(outputStr) && JSONValidator.from(outputStr).getType() == JSONValidator.Type.Object) {
            output = JSON.parseObject(outputStr);
        } else {
            Map<String, Object> rs = new HashMap<>();
            rs.put("output", outputStr);
            output = rs;
        }
        List<InspectWorkbenchQueryLlmTreeDTO.FieldMapping> outputFieldMappingList = convertOutputFieldMappings(aidaAppTreeDTOFirst.getOutputFieldMapping());
        aidaLlmTree.setOutputFieldMapping(outputFieldMappingList);
        aidaLlmTree.setOutput(output);
        return aidaLlmTree;
    }

    private static String buildSelectedNodeId(Map<String, String> customConfigMap, InspectWorkbenchQueryDetailDTO inspectWorkbenchQueryDetail) {
        String config = customConfigMap.get(CustomConfigTypeEnum.SELECTED_NODE.getCode());
        boolean exist = inspectWorkbenchQueryDetail.getSignal().stream()
                .anyMatch(aidaSignal -> StringUtils.equals(aidaSignal.getNodeId(), config));
        if (exist) {
            return config;
        }
        return null;
    }

    private static WorkbenchCustomConfigCondition buildWorkbenchCustomConfigCondition(String appId, String platformWorkspace, String appVersionId, CustomConfigTypeEnum type, String login) {
        WorkbenchCustomConfigCondition condition = new WorkbenchCustomConfigCondition();
        condition.setType(type.getCode());
        condition.setPlatformWorkspace(platformWorkspace);
        condition.setAppId(appId);
        condition.setAppVersionId(appVersionId);
        condition.setCreatorMis(login);
        return condition;
    }

    private void saveOrUpdateCustomConfig(String appId, String platformWorkspace, String appVersionId, String config, WorkbenchCustomConfigPo workbenchCustomConfigPo, String login, CustomConfigTypeEnum type) {
        Date date = new Date();

        WorkbenchCustomConfigPo workbenchCustomConfig = new WorkbenchCustomConfigPo();
        workbenchCustomConfig.setConfig(config);
        if (workbenchCustomConfigPo != null) {
            workbenchCustomConfig.setId(workbenchCustomConfigPo.getId());
            workbenchCustomConfig.setUpdaterMis(login);
            workbenchCustomConfig.setGmtModified(date);
            workbenchCustomConfigGeneratorService.updateById(workbenchCustomConfig);
        } else {
            workbenchCustomConfig.setGmtCreated(date);
            workbenchCustomConfig.setCreatorMis(login);
            workbenchCustomConfig.setUpdaterMis(login);
            workbenchCustomConfig.setGmtModified(date);
            workbenchCustomConfig.setAppId(appId);
            workbenchCustomConfig.setPlatformWorkspace(platformWorkspace);
            workbenchCustomConfig.setAppVersionId(appVersionId);
            workbenchCustomConfig.setType(type.getCode());
            workbenchCustomConfigGeneratorService.save(workbenchCustomConfig);
        }
    }

    /***
     * 获取lion配置
     * @param aidaWorkFlowDebugWhiteListNode
     * @return
     */
    private static List<String> getLionWorkFlowDebugConfig(String aidaWorkFlowDebugWhiteListNode) {
        String debugWhiteList = Lion.getString(ConfigUtil.getAppkey(), aidaWorkFlowDebugWhiteListNode);
        List<String> nodeDebugWhiteList = new ArrayList<>();
        if (StringUtils.isNotBlank(debugWhiteList)) {
            List<String> nodeDebugWhiteListConfig = JSON.parseArray(debugWhiteList, InspectWorkbenchLlmNodeDebugConfig.class).stream()
                    .map(InspectWorkbenchLlmNodeDebugConfig::getAppId)
                    .collect(Collectors.toList());
            nodeDebugWhiteList.addAll(nodeDebugWhiteListConfig);
        }
        return nodeDebugWhiteList;
    }

    /***
     * 过滤选择的appId
     * @param rootList
     * @param selectedAppId
     * @return
     */
    public static String filterSelectedAppId(List<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> rootList, String selectedAppId) {
        InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree root = rootList.stream().findFirst().orElse(null);
        if (root == null) {
            return null;
        }

        // 使用队列来模拟递归
        Queue<InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree> queue = new LinkedList<>();
        queue.offer(root);
        // 当队列不为空时继续处理
        while (!queue.isEmpty()) {
            // 获取当前节点
            InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree currentNode = queue.poll();

            if (StringUtils.equals(currentNode.getAppId(), selectedAppId)) {
                return selectedAppId;
            }

            // 将当前节点的子节点加入队列
            if (CollectionUtils.isNotEmpty(currentNode.getChildList())) {
                for (InspectWorkbenchQueryLlmTreeDTO.AidaLlmTree child : currentNode.getChildList()) {
                    queue.offer(child);
                }
            }
        }
        return null;
    }

    /***
     * 提取熔断信息
     * @param logList
     * @return
     */
    private List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> convertFuse(List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList, String appId) {
        List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfoList = new ArrayList<>();
        try {
            // step 1 找出最外层输出节点 判断是否熔断
            boolean fuse = logList.stream()
                    .filter(traceLog -> StringUtils.equals(traceLog.getApplicationId(), appId))
                    .filter(traceLog -> StringUtils.equals(traceLog.getNodeType(), REPLY_NODE_TYPE))
                    .filter(traceLog -> StringUtils.isNotBlank(traceLog.getOutput()))
                    .filter(traceLog -> JSONValidator.from(traceLog.getOutput()).getType() == JSONValidator.Type.Object)
                    .map(traceLog -> JSON.parseObject(traceLog.getOutput()))
                    .filter(Objects::nonNull)
                    .filter(jsonObject -> jsonObject.containsKey("endType"))
                    .anyMatch(jsonObject -> !StringUtils.equals(jsonObject.getString("endType"), "1"));

            // step 2 倒叙找出最近熔断信息
            if (fuse) {
                List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfos = convertFuseBasicInfoList(logList);
                if (CollectionUtils.isNotEmpty(basicInfos)) {
                    basicInfoList.addAll(basicInfos);
                } else {
                    basicInfoList.add(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("熔断类型", "未识别"));
                    basicInfoList.add(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("熔断标签", "未识别"));
                }
                return basicInfoList;
            }
            return basicInfoList;
        } catch (Exception e) {
            log.error("InspectWorkbench 解析熔断信息异常 appId:{},logList:{}", appId, JSON.toJSONString(logList), e);
        }
        return basicInfoList;
    }

    private static ArrayList<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> convertFuseBasicInfoList(List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList) {
        // 如果 JSON 中不包含需要的 key，跳过当前循环
        // 检查 completeContent 是否为空或不符合 JSON 对象类型
        return new ArrayList<>(logList.stream()
                .sorted(Comparator.comparing(InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getStartTime, Collections.reverseOrder()))
                .filter(traceLog -> StringUtils.isNotBlank(traceLog.getOutput()))
                .filter(traceLog -> JSONValidator.from(traceLog.getOutput()).getType() == JSONValidator.Type.Object)
                .flatMap(traceLog -> {
                    JSONObject jsonObject = JSON.parseObject(traceLog.getOutput());
                    String completeContentKey = traceLog.getNodeId() + "__complete_content_";
                    // 如果 JSON 中不包含需要的 key，跳过当前循环
                    if (jsonObject != null && jsonObject.containsKey(completeContentKey)) {
                        List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfoListFuse = new ArrayList<>();
                        String completeContent = jsonObject.getString(completeContentKey);
                        // 检查 completeContent 是否为空或不符合 JSON 对象类型
                        if (StringUtils.isBlank(completeContent) || JSONValidator.from(completeContent).getType() != JSONValidator.Type.Object) {
                            return Stream.empty();
                        }

                        JSONObject completeContentObject = JSON.parseObject(completeContent);
                        String fuseType = completeContentObject.getString("fuseType");
                        String fuseLabel = completeContentObject.getString("fuseLabel");
                        if (StringUtils.isNotBlank(fuseType)) {
                            InspectWorkbenchQueryDetailDTO.Detail.BasicInfo fuseTypeInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("熔断类型", fuseType);
                            basicInfoListFuse.add(fuseTypeInfo);
                        }
                        if (StringUtils.isNotBlank(fuseLabel)) {
                            InspectWorkbenchQueryDetailDTO.Detail.BasicInfo fuseLabelInfo = new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("熔断标签", fuseLabel);
                            basicInfoListFuse.add(fuseLabelInfo);
                        }
                        return basicInfoListFuse.stream();
                    }
                    return Stream.empty();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(InspectWorkbenchQueryDetailDTO.Detail.BasicInfo::getDisplayName, Function.identity(), (a, b) -> a))
                .values());
    }


    /***
     * 提取执行动作
     * @param logList
     * @return
     */
    private static InspectWorkbenchQueryDetailDTO.Detail.BasicInfo convertAction(List<InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> logList) {
        // 取执行动作
        String actionConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_ACTION_NODE_INFO);
        if (StringUtils.isNotBlank(actionConfig)) {
            // AI搭的工具节点是通用能力，无法得知哪个节点是用来执行动作的，因此将动作节点配置在Lion上
            Map<String, Map<String, String>> actionMap = JSONObject.parseObject(actionConfig, new TypeReference<Map<String, Map<String, String>>>() {
            });
            Map<String, Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace>> logMap = logList.stream()
                    .collect(Collectors.groupingBy(
                            InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getApplicationId,
                            Collectors.toMap(
                                    InspectWorkbenchQueryDetailDTO.Detail.AidaTrace::getNodeId,
                                    Function.identity(), (a, b) -> a
                            )
                    ));
            List<String> actionList = new ArrayList<>();
            for (Map.Entry<String, Map<String, String>> entry : actionMap.entrySet()) {
                String applicationId = entry.getKey();
                Map<String, String> actionInfo = entry.getValue();
                if (logMap.containsKey(applicationId)) {
                    Map<String, InspectWorkbenchQueryDetailDTO.Detail.AidaTrace> appNodeLog = logMap.get(applicationId);
                    for (Map.Entry<String, String> actionEntry : actionInfo.entrySet()) {
                        String nodeId = actionEntry.getKey();
                        if (appNodeLog.containsKey(nodeId)) {
                            String actionName = StringUtils.isNotBlank(actionEntry.getValue()) ? actionEntry.getValue() : appNodeLog.get(nodeId).getNodeName();
                            actionList.add(actionName);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(actionList)) {
                return new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo("执行动作", String.join(",", actionList));
            }
        }
        return null;
    }

    private static void convertBaseInfoList(InspectWorkbenchSessionDetailDTO dto, Map<String, List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo>> fuseMap, Map<String, InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> actionMap) {
        if (dto.getMessageLevelAidaAppInfo() != null && StringUtils.isNotBlank(dto.getMessageLevelAidaAppInfo().getLlmMessageId())) {
            List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfo = Lists.newArrayList();
            List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> fuseList = fuseMap.get(dto.getMessageLevelAidaAppInfo().getLlmMessageId());
            if (CollectionUtils.isNotEmpty(fuseList)) {
                basicInfo.addAll(fuseList);
            }
            InspectWorkbenchQueryDetailDTO.Detail.BasicInfo action = actionMap.get(dto.getMessageLevelAidaAppInfo().getLlmMessageId());
            if (action != null) {
                basicInfo.add(action);
            }
            dto.setBasicInfo(basicInfo);
        }
    }

    @Data
    public static class WorkflowLogResult implements Serializable {
        /**
         * 工作流执行链路
         */
        private Future<List<WorkflowNodeExeDetailsDTO>> workflowNodeExeDetailListFuture;
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 大模型消息ID
         */
        private String messageId;
    }
}
